/* ===== 动画关键帧定义 ===== */

/* 滑入动画 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 摇摆动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* 旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes spinSlow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 打字机效果 */
@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--accent-blue);
    }
}

/* 加载动画 */
@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes loadingDots {
    0%, 20% {
        color: var(--text-quaternary);
        transform: scale(1);
    }
    50% {
        color: var(--accent-blue);
        transform: scale(1.2);
    }
    80%, 100% {
        color: var(--text-quaternary);
        transform: scale(1);
    }
}

/* 进度条动画 */
@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--progress-width, 100%);
    }
}

/* 成功动画 */
@keyframes checkmark {
    0% {
        stroke-dashoffset: 100;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

/* 错误动画 */
@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* 数字计数动画 */
@keyframes countUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== 动画类定义 ===== */

/* 基础动画类 */
.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

.animate-fade-in-scale {
    animation: fadeInScale 0.3s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.animate-slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.animate-slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

/* 循环动画类 */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-spin-slow {
    animation: spinSlow 3s linear infinite;
}

/* 交互动画类 */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* 状态动画类 */
.loading-dots::after {
    content: '...';
    animation: loadingDots 1.5s infinite;
}

.typing-indicator {
    overflow: hidden;
    white-space: nowrap;
    border-right: 2px solid var(--accent-blue);
    animation: typing 2s steps(40, end), blink 1s step-end infinite;
}

.success-animation {
    animation: successPulse 0.6s ease-out;
}

.error-animation {
    animation: errorShake 0.5s ease-in-out;
}

/* 进入/退出动画 */
.enter-animation {
    animation: fadeInScale 0.3s ease-out;
}

.exit-animation {
    animation: fadeIn 0.3s ease-out reverse;
}

/* 延迟动画类 */
.delay-100 {
    animation-delay: 100ms;
}

.delay-200 {
    animation-delay: 200ms;
}

.delay-300 {
    animation-delay: 300ms;
}

.delay-500 {
    animation-delay: 500ms;
}

.delay-700 {
    animation-delay: 700ms;
}

.delay-1000 {
    animation-delay: 1000ms;
}

/* 持续时间类 */
.duration-fast {
    animation-duration: 150ms;
}

.duration-normal {
    animation-duration: 300ms;
}

.duration-slow {
    animation-duration: 500ms;
}

.duration-slower {
    animation-duration: 1000ms;
}

/* 特殊效果动画 */
.gradient-animation {
    background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #10b981, #f59e0b);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 文字动画 */
.text-glow {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from {
        text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    }
    to {
        text-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    }
}

/* 响应式动画控制 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 性能优化 */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}

/* GPU加速 */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== 高级过渡动画 ===== */

/* 页面切换动画 */
@keyframes pageSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pageSlideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-30px);
    }
}

/* 卡片翻转动画 */
@keyframes cardFlip {
    0% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(90deg);
    }
    100% {
        transform: rotateY(0deg);
    }
}

/* 弹性进入动画 */
@keyframes elasticIn {
    0% {
        opacity: 0;
        transform: scale(0.3) rotate(-10deg);
    }
    50% {
        transform: scale(1.05) rotate(2deg);
    }
    70% {
        transform: scale(0.9) rotate(-1deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* 磁性吸附动画 */
@keyframes magneticAttract {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1.05);
    }
}

/* 液体波动动画 */
@keyframes liquidWave {
    0%, 100% {
        border-radius: 50% 40% 30% 60%;
        transform: rotate(0deg);
    }
    25% {
        border-radius: 60% 30% 50% 40%;
        transform: rotate(90deg);
    }
    50% {
        border-radius: 30% 60% 40% 50%;
        transform: rotate(180deg);
    }
    75% {
        border-radius: 40% 50% 60% 30%;
        transform: rotate(270deg);
    }
}

/* 粒子散射动画 */
@keyframes particleScatter {
    0% {
        opacity: 1;
        transform: scale(1) translate(0, 0);
    }
    100% {
        opacity: 0;
        transform: scale(0.3) translate(var(--random-x, 50px), var(--random-y, -50px));
    }
}

/* 光线扫描动画 */
@keyframes lightScan {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 呼吸灯效果 */
@keyframes breathe {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.05);
        opacity: 1;
    }
}

/* 高级动画类 */
.animate-page-slide-in {
    animation: pageSlideIn 0.5s ease-out;
}

.animate-page-slide-out {
    animation: pageSlideOut 0.5s ease-out;
}

.animate-card-flip {
    animation: cardFlip 0.8s ease-in-out;
}

.animate-elastic-in {
    animation: elasticIn 0.6s ease-out;
}

.animate-magnetic-attract {
    animation: magneticAttract 0.3s ease-out;
}

.animate-liquid-wave {
    animation: liquidWave 4s ease-in-out infinite;
}

.animate-particle-scatter {
    animation: particleScatter 1s ease-out forwards;
}

.animate-light-scan {
    animation: lightScan 2s ease-in-out infinite;
}

.animate-breathe {
    animation: breathe 3s ease-in-out infinite;
}

/* 交互状态动画 */
.interactive-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.interactive-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.interactive-hover:hover::before {
    left: 100%;
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 状态指示器动画 */
.status-pulse {
    position: relative;
}

.status-pulse::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    transform: translate(-50%, -50%);
    animation: pulse 2s ease-in-out infinite;
    opacity: 0.6;
}

/* 加载骨架动画 */
@keyframes skeleton {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200px 100%;
    animation: skeleton 1.5s infinite;
}

/* 成功庆祝动画 */
@keyframes celebrate {
    0% {
        transform: scale(1) rotate(0deg);
    }
    25% {
        transform: scale(1.2) rotate(5deg);
    }
    50% {
        transform: scale(1.1) rotate(-3deg);
    }
    75% {
        transform: scale(1.15) rotate(2deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
    }
}

.animate-celebrate {
    animation: celebrate 0.6s ease-in-out;
}

/* 错误摇摆动画 */
@keyframes errorWiggle {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px) rotate(-1deg);
    }
    75% {
        transform: translateX(5px) rotate(1deg);
    }
}

.animate-error-wiggle {
    animation: errorWiggle 0.5s ease-in-out;
}

/* 注意力吸引动画 */
@keyframes attention {
    0% {
        transform: scale(1);
    }
    15% {
        transform: scale(1.1);
    }
    30% {
        transform: scale(1);
    }
    45% {
        transform: scale(1.05);
    }
    60% {
        transform: scale(1);
    }
}

.animate-attention {
    animation: attention 2s ease-in-out;
}

/* 平滑滚动 */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* 视差滚动 */
.parallax {
    transform: translateZ(0);
    will-change: transform;
}
