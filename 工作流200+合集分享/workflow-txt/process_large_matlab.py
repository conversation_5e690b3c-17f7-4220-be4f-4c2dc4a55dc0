#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理大型MATLAB .mat文件并转换为JSON格式（优化版本）
"""

import scipy.io
import json
import numpy as np
import os
import sys
from datetime import datetime

def safe_convert_numpy_to_json(obj, max_size=1000):
    """安全地将numpy数组转换为JSON可序列化的格式，限制大小"""
    if isinstance(obj, np.ndarray):
        # 如果数组太大，只取前几个元素
        if obj.size > max_size:
            print(f"  警告: 数组太大 ({obj.size} 元素)，只取前 {max_size} 个元素")
            if obj.ndim == 1:
                obj = obj[:max_size]
            elif obj.ndim == 2:
                obj = obj[:min(max_size, obj.shape[0]), :min(max_size, obj.shape[1])]
            else:
                # 对于多维数组，展平后取前max_size个元素
                obj = obj.flatten()[:max_size]
        
        if obj.dtype.kind in ['U', 'S']:  # 字符串数组
            return obj.tolist()
        elif obj.dtype.kind in ['i', 'u', 'f']:  # 数值数组
            return obj.tolist()
        elif obj.dtype.kind == 'O':  # 对象数组
            return [safe_convert_numpy_to_json(item, max_size//10) for item in obj[:min(10, len(obj))]]
        else:
            return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, (list, tuple)):
        # 限制列表长度
        if len(obj) > max_size:
            print(f"  警告: 列表太长 ({len(obj)} 元素)，只取前 {max_size} 个元素")
            obj = obj[:max_size]
        return [safe_convert_numpy_to_json(item, max_size//10) for item in obj]
    elif isinstance(obj, dict):
        return {key: safe_convert_numpy_to_json(value, max_size//10) for key, value in list(obj.items())[:max_size]}
    else:
        return obj

def process_large_matlab_file(file_path):
    """处理大型MATLAB文件并转换为JSON"""
    try:
        print(f"正在读取大型MATLAB文件: {file_path}")
        print("这可能需要一些时间...")
        
        # 读取MATLAB文件
        mat_data = scipy.io.loadmat(file_path)
        
        print("文件读取成功！")
        print(f"包含的键: {list(mat_data.keys())}")
        
        # 过滤掉系统变量（以__开头的键）
        filtered_data = {}
        for key, value in mat_data.items():
            if not key.startswith('__'):
                print(f"处理键: {key}")
                print(f"  类型: {type(value).__name__}")
                if hasattr(value, 'shape'):
                    print(f"  形状: {value.shape}")
                    print(f"  大小: {value.size} 元素")
                    print(f"  数据类型: {value.dtype}")
                
                try:
                    filtered_data[key] = safe_convert_numpy_to_json(value)
                    print(f"  ✅ 成功处理")
                except Exception as e:
                    print(f"  ❌ 处理失败: {e}")
                    # 如果处理失败，保存基本信息
                    filtered_data[key] = {
                        "error": str(e),
                        "type": type(value).__name__,
                        "shape": getattr(value, 'shape', 'N/A'),
                        "size": getattr(value, 'size', 'N/A'),
                        "dtype": str(getattr(value, 'dtype', 'N/A'))
                    }
        
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_file = f"{base_name}_converted.json"
        
        # 保存为JSON文件
        print(f"正在保存到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成！输出文件: {output_file}")
        
        # 显示数据摘要
        print("\n数据摘要:")
        for key, value in filtered_data.items():
            if isinstance(value, list):
                print(f"  {key}: 列表，长度 {len(value)}")
                if len(value) > 0:
                    print(f"    第一个元素类型: {type(value[0]).__name__}")
            elif isinstance(value, dict) and "error" in value:
                print(f"  {key}: 处理失败 - {value['error']}")
            else:
                print(f"  {key}: {type(value).__name__}")
        
        return output_file
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 处理当前目录下的MATLAB文件
    matlab_file = "Workflow-DZ052_huochairenxinli2_1-draft-7052.zip"
    
    if os.path.exists(matlab_file):
        output_file = process_large_matlab_file(matlab_file)
        if output_file:
            print(f"\n✅ 成功将MATLAB文件转换为JSON: {output_file}")
        else:
            print("❌ 转换失败")
    else:
        print(f"文件不存在: {matlab_file}") 