#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境检查和依赖验证脚本
检查系统环境是否满足工作流转换器的运行要求
"""

import sys
import os
import json
import zipfile
import shutil
import re
from pathlib import Path
from datetime import datetime

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"
    
    print(f"  当前Python版本: {version_str}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("  ❌ Python版本过低，需要Python 3.6或更高版本")
        return False
    else:
        print("  ✅ Python版本满足要求")
        return True

def check_required_modules():
    """检查必需的模块"""
    print("\n📦 检查必需模块...")
    
    required_modules = [
        ('os', '操作系统接口'),
        ('json', 'JSON数据处理'),
        ('zipfile', 'ZIP文件处理'),
        ('shutil', '文件操作工具'),
        ('re', '正则表达式'),
        ('pathlib', '路径处理'),
        ('datetime', '日期时间处理'),
        ('argparse', '命令行参数解析'),
        ('sys', '系统相关功能')
    ]
    
    all_available = True
    
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name} - {description}")
        except ImportError:
            print(f"  ❌ {module_name} - {description} (未找到)")
            all_available = False
    
    if all_available:
        print("  🎉 所有必需模块都可用")
    else:
        print("  ⚠️ 部分模块缺失，可能影响程序运行")
    
    return all_available

def check_file_permissions():
    """检查文件权限"""
    print("\n🔐 检查文件权限...")
    
    current_dir = Path(".")
    
    # 检查读取权限
    if os.access(current_dir, os.R_OK):
        print("  ✅ 当前目录可读")
    else:
        print("  ❌ 当前目录不可读")
        return False
    
    # 检查写入权限
    if os.access(current_dir, os.W_OK):
        print("  ✅ 当前目录可写")
    else:
        print("  ❌ 当前目录不可写")
        return False
    
    # 测试创建目录
    test_dir = current_dir / "test_permissions"
    try:
        test_dir.mkdir(exist_ok=True)
        test_dir.rmdir()
        print("  ✅ 可以创建和删除目录")
    except Exception as e:
        print(f"  ❌ 无法创建目录: {e}")
        return False
    
    return True

def check_disk_space():
    """检查磁盘空间"""
    print("\n💾 检查磁盘空间...")
    
    try:
        current_dir = Path(".")
        stat = os.statvfs(current_dir)
        
        # 计算可用空间 (字节)
        available_bytes = stat.f_bavail * stat.f_frsize
        available_mb = available_bytes / (1024 * 1024)
        available_gb = available_mb / 1024
        
        print(f"  可用磁盘空间: {available_gb:.2f} GB ({available_mb:.0f} MB)")
        
        if available_mb < 100:
            print("  ⚠️ 磁盘空间不足100MB，可能影响大文件处理")
            return False
        elif available_mb < 1024:
            print("  ⚠️ 磁盘空间较少，建议清理后再处理大量文件")
        else:
            print("  ✅ 磁盘空间充足")
        
        return True
        
    except Exception as e:
        print(f"  ⚠️ 无法检查磁盘空间: {e}")
        return True  # 不阻止程序运行

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    # 测试JSON处理
    try:
        test_data = {"test": "data", "number": 123}
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        print("  ✅ JSON处理功能正常")
    except Exception as e:
        print(f"  ❌ JSON处理失败: {e}")
        return False
    
    # 测试文件操作
    try:
        test_file = Path("test_file.tmp")
        test_file.write_text("测试内容", encoding='utf-8')
        content = test_file.read_text(encoding='utf-8')
        test_file.unlink()
        print("  ✅ 文件读写功能正常")
    except Exception as e:
        print(f"  ❌ 文件操作失败: {e}")
        return False
    
    # 测试正则表达式
    try:
        pattern = r'[\x00-\x1f\x7f-\x9f]'
        test_str = "测试\x00字符串"
        cleaned = re.sub(pattern, '', test_str)
        print("  ✅ 正则表达式功能正常")
    except Exception as e:
        print(f"  ❌ 正则表达式失败: {e}")
        return False
    
    return True

def check_sample_files():
    """检查示例文件"""
    print("\n📁 检查示例文件...")
    
    current_dir = Path(".")
    
    # 查找ZIP文件
    zip_files = list(current_dir.glob("*.zip"))
    if zip_files:
        print(f"  找到 {len(zip_files)} 个ZIP文件:")
        for zip_file in zip_files[:5]:  # 只显示前5个
            size_mb = zip_file.stat().st_size / 1024 / 1024
            print(f"    - {zip_file.name} ({size_mb:.1f}MB)")
        if len(zip_files) > 5:
            print(f"    ... 还有 {len(zip_files) - 5} 个文件")
    else:
        print("  ⚠️ 未找到ZIP文件，请确保工作流文件在当前目录")
    
    # 查找其他支持的文件
    other_files = []
    for pattern in ['*.mat', '*.json']:
        other_files.extend(current_dir.glob(pattern))
    
    if other_files:
        print(f"  找到 {len(other_files)} 个其他支持的文件")
    
    return len(zip_files) > 0 or len(other_files) > 0

def check_scripts():
    """检查脚本文件"""
    print("\n📜 检查脚本文件...")
    
    scripts = [
        ('batch_workflow_converter.py', '完整版转换器'),
        ('quick_txt_converter.py', '快速TXT转换器'),
        ('convert_workflows.py', '原版转换器'),
        ('优化版使用说明.md', '使用说明文档')
    ]
    
    all_present = True
    
    for script_name, description in scripts:
        script_path = Path(script_name)
        if script_path.exists():
            size_kb = script_path.stat().st_size / 1024
            print(f"  ✅ {script_name} - {description} ({size_kb:.1f}KB)")
        else:
            print(f"  ❌ {script_name} - {description} (未找到)")
            all_present = False
    
    return all_present

def generate_report():
    """生成环境检查报告"""
    print("\n📋 生成环境检查报告...")
    
    report = {
        "检查时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "系统信息": {
            "操作系统": os.name,
            "Python版本": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "Python路径": sys.executable,
            "当前目录": str(Path(".").absolute())
        },
        "检查结果": {
            "Python版本": "通过" if check_python_version() else "失败",
            "必需模块": "通过" if check_required_modules() else "失败", 
            "文件权限": "通过" if check_file_permissions() else "失败",
            "基本功能": "通过" if test_basic_functionality() else "失败"
        }
    }
    
    try:
        report_file = Path("environment_check_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 报告已保存到: {report_file}")
    except Exception as e:
        print(f"  ⚠️ 无法保存报告: {e}")

def main():
    """主函数"""
    print("🔍 工作流转换器环境检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("必需模块", check_required_modules),
        ("文件权限", check_file_permissions),
        ("磁盘空间", check_disk_space),
        ("基本功能", test_basic_functionality),
        ("示例文件", check_sample_files),
        ("脚本文件", check_scripts)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"  ❌ 检查 {check_name} 时出错: {e}")
            results[check_name] = False
    
    # 生成报告
    generate_report()
    
    # 显示总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 环境检查全部通过，可以正常使用转换器！")
    elif passed >= total - 2:
        print("⚠️ 大部分检查通过，可以尝试使用转换器")
    else:
        print("❌ 多项检查失败，建议解决问题后再使用")
    
    print("\n💡 使用建议:")
    if results.get("示例文件", False):
        print("  - 可以直接运行转换器处理现有文件")
    else:
        print("  - 请先将工作流ZIP文件放在当前目录")
    
    if results.get("脚本文件", False):
        print("  - 推荐使用 quick_txt_converter.py 进行快速转换")
        print("  - 或使用 batch_workflow_converter.py 进行完整转换")
    
    print("  - 详细使用方法请查看 优化版使用说明.md")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        sys.exit(1)
