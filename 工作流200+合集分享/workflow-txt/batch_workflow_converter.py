#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版批量工作流文件转换器
将多个工作流ZIP文件转换为可读的JSON和TXT文件
支持进度显示、详细错误处理和多种输出格式
"""

import os
import json
import zipfile
import shutil
import re
import sys
import time
from pathlib import Path
from datetime import datetime
import argparse
from typing import Dict, List, Optional, Tuple

class ProgressBar:
    """简单的进度条显示类"""
    def __init__(self, total: int, width: int = 50):
        self.total = total
        self.width = width
        self.current = 0

    def update(self, current: int, message: str = ""):
        self.current = current
        percent = (current / self.total) * 100
        filled = int(self.width * current // self.total)
        bar = '█' * filled + '░' * (self.width - filled)

        # 清除当前行并显示进度条
        sys.stdout.write(f'\r[{bar}] {percent:.1f}% ({current}/{self.total}) {message}')
        sys.stdout.flush()

    def finish(self, message: str = "完成"):
        self.update(self.total, message)
        print()  # 换行

class WorkflowConverter:
    def __init__(self, input_dir=".", output_dir="converted_workflows", output_format="both"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_format = output_format  # "json", "txt", "both"
        self.output_dir.mkdir(exist_ok=True)

        # 创建子目录
        if self.output_format in ["json", "both"]:
            (self.output_dir / "json").mkdir(exist_ok=True)
        if self.output_format in ["txt", "both"]:
            (self.output_dir / "txt").mkdir(exist_ok=True)

        # 创建日志文件
        self.log_file = self.output_dir / "conversion_log.txt"
        self.success_count = 0
        self.failed_count = 0
        self.processed_files = []
        self.start_time = None
        
    def log_message(self, message: str, level: str = "INFO"):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"

        # 根据级别决定是否打印到控制台
        if level in ["INFO", "SUCCESS", "ERROR", "WARNING"]:
            if level == "ERROR":
                print(f"❌ {message}")
            elif level == "SUCCESS":
                print(f"✅ {message}")
            elif level == "WARNING":
                print(f"⚠️ {message}")
            else:
                print(f"ℹ️ {message}")

        # 写入日志文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')

    def format_workflow_as_txt(self, data: Dict) -> str:
        """将工作流数据格式化为可读的TXT格式"""
        lines = []
        lines.append("=" * 80)
        lines.append("工作流配置文件")
        lines.append("=" * 80)
        lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        # 版本信息
        if 'versions' in data:
            lines.append("📋 版本信息:")
            lines.append("-" * 40)
            for key, value in data['versions'].items():
                lines.append(f"  {key}: {value}")
            lines.append("")

        # 节点信息
        if 'nodes' in data:
            lines.append(f"🔗 节点信息 (共 {len(data['nodes'])} 个节点):")
            lines.append("-" * 40)
            for i, node in enumerate(data['nodes'], 1):
                lines.append(f"节点 {i}:")
                lines.append(f"  ID: {node.get('id', 'N/A')}")
                lines.append(f"  类型: {node.get('type', 'N/A')}")
                lines.append(f"  位置: ({node.get('position', {}).get('x', 'N/A')}, {node.get('position', {}).get('y', 'N/A')})")

                # 节点数据
                if 'data' in node:
                    lines.append("  配置:")
                    for key, value in node['data'].items():
                        if isinstance(value, (dict, list)):
                            lines.append(f"    {key}: {type(value).__name__} (详见JSON文件)")
                        else:
                            lines.append(f"    {key}: {value}")
                lines.append("")

        # 连接信息
        if 'edges' in data:
            lines.append(f"🔀 连接信息 (共 {len(data['edges'])} 个连接):")
            lines.append("-" * 40)
            for i, edge in enumerate(data['edges'], 1):
                lines.append(f"连接 {i}:")
                lines.append(f"  ID: {edge.get('id', 'N/A')}")
                lines.append(f"  源节点: {edge.get('source', 'N/A')}")
                lines.append(f"  目标节点: {edge.get('target', 'N/A')}")
                lines.append(f"  源端口: {edge.get('sourceHandle', 'N/A')}")
                lines.append(f"  目标端口: {edge.get('targetHandle', 'N/A')}")
                lines.append("")

        lines.append("=" * 80)
        lines.append("注意: 完整的技术细节请查看对应的JSON文件")
        lines.append("=" * 80)

        return '\n'.join(lines)
    
    def extract_json_from_file(self, file_path: Path) -> Optional[Dict]:
        """从文件中提取JSON数据，支持多种编码格式"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']

        for encoding in encodings:
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()

                # 尝试不同的编码
                try:
                    content_str = content.decode(encoding)
                except UnicodeDecodeError:
                    continue

                # 尝试找到JSON开始的位置
                json_start = content_str.find('{')

                if json_start == -1:
                    continue

                # 提取JSON部分
                json_content = content_str[json_start:]

                # 尝试解析JSON
                try:
                    data = json.loads(json_content)
                    self.log_message(f"成功使用 {encoding} 编码提取JSON数据", "SUCCESS")
                    return data
                except json.JSONDecodeError:
                    # 尝试修复JSON
                    fixed_json = self.fix_json_content(json_content)
                    if fixed_json:
                        try:
                            data = json.loads(fixed_json)
                            self.log_message(f"成功修复并使用 {encoding} 编码提取JSON数据", "SUCCESS")
                            return data
                        except json.JSONDecodeError:
                            continue

            except Exception as e:
                self.log_message(f"使用 {encoding} 编码提取JSON失败: {e}", "WARNING")
                continue

        self.log_message(f"所有编码尝试失败，无法提取JSON数据: {file_path.name}", "ERROR")
        return None
    
    def fix_json_content(self, json_content: str) -> Optional[str]:
        """尝试修复JSON内容，支持多种修复策略"""
        try:
            # 策略1: 移除可能的控制字符
            cleaned_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_content)

            # 策略2: 尝试找到完整的JSON对象
            brace_count = 0
            end_pos = 0

            for i, char in enumerate(cleaned_content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = i + 1
                        break

            if end_pos > 0:
                candidate = cleaned_content[:end_pos]

                # 策略3: 修复常见的JSON格式问题
                # 移除尾随逗号
                candidate = re.sub(r',(\s*[}\]])', r'\1', candidate)

                # 修复单引号为双引号
                candidate = re.sub(r"'([^']*)':", r'"\1":', candidate)

                # 验证修复后的JSON
                try:
                    json.loads(candidate)
                    return candidate
                except json.JSONDecodeError:
                    pass

            return None

        except Exception as e:
            self.log_message(f"修复JSON失败: {e}", "ERROR")
            return None
    
    def process_single_file(self, file_path: Path) -> bool:
        """处理单个文件，支持更多文件类型和错误恢复"""
        file_name = file_path.name
        self.log_message(f"开始处理: {file_name}")

        try:
            # 检查文件大小
            file_size = file_path.stat().st_size
            if file_size == 0:
                self.log_message(f"跳过空文件: {file_name}", "WARNING")
                return False

            if file_size > 100 * 1024 * 1024:  # 100MB
                self.log_message(f"文件过大 ({file_size / 1024 / 1024:.1f}MB): {file_name}", "WARNING")
                response = input(f"文件 {file_name} 较大，是否继续处理？(y/N): ")
                if response.lower() != 'y':
                    return False

            # 检查文件类型
            if file_path.suffix.lower() == '.zip':
                return self.process_zip_file(file_path)
            elif file_path.suffix.lower() in ['.json', '.mat']:
                return self.process_direct_file(file_path)
            else:
                self.log_message(f"跳过不支持的文件类型: {file_name}", "WARNING")
                return False

        except PermissionError:
            self.log_message(f"权限不足，无法访问文件: {file_name}", "ERROR")
            return False
        except Exception as e:
            self.log_message(f"处理文件失败 {file_name}: {e}", "ERROR")
            return False
    
    def process_zip_file(self, zip_path):
        """处理ZIP文件"""
        try:
            # 创建临时目录
            temp_dir = self.output_dir / f"temp_{zip_path.stem}"
            temp_dir.mkdir(exist_ok=True)
            
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找MATLAB文件
            matlab_files = list(temp_dir.glob("*.zip"))  # 通常是嵌套的ZIP
            if not matlab_files:
                matlab_files = list(temp_dir.glob("*.mat"))
            
            if not matlab_files:
                self.log_message(f"在ZIP中未找到MATLAB文件: {zip_path.name}")
                shutil.rmtree(temp_dir)
                return False
            
            # 处理找到的文件
            success = False
            for matlab_file in matlab_files:
                if self.process_matlab_file(matlab_file, zip_path.stem):
                    success = True
                    break
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            return success
            
        except Exception as e:
            self.log_message(f"处理ZIP文件失败 {zip_path.name}: {e}")
            return False
    
    def process_direct_file(self, file_path):
        """直接处理文件"""
        return self.process_matlab_file(file_path, file_path.stem)
    
    def process_matlab_file(self, file_path: Path, base_name: str) -> bool:
        """处理MATLAB文件，支持多种输出格式"""
        try:
            # 提取JSON数据
            data = self.extract_json_from_file(file_path)
            if not data:
                self.log_message(f"无法提取JSON数据: {file_path.name}", "ERROR")
                return False

            # 验证数据结构
            if not self.validate_workflow_data(data):
                self.log_message(f"工作流数据结构无效: {file_path.name}", "WARNING")

            success = True

            # 生成JSON文件
            if self.output_format in ["json", "both"]:
                json_file = self.output_dir / "json" / f"{base_name}_workflow.json"
                summary_file = self.output_dir / "json" / f"{base_name}_summary.json"

                try:
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)

                    self.create_summary_file(data, json_file, summary_file)
                    self.log_message(f"JSON文件已生成: {json_file.name}", "SUCCESS")
                except Exception as e:
                    self.log_message(f"生成JSON文件失败: {e}", "ERROR")
                    success = False

            # 生成TXT文件
            if self.output_format in ["txt", "both"]:
                txt_file = self.output_dir / "txt" / f"{base_name}_workflow.txt"

                try:
                    txt_content = self.format_workflow_as_txt(data)
                    with open(txt_file, 'w', encoding='utf-8') as f:
                        f.write(txt_content)

                    self.log_message(f"TXT文件已生成: {txt_file.name}", "SUCCESS")
                except Exception as e:
                    self.log_message(f"生成TXT文件失败: {e}", "ERROR")
                    success = False

            if success:
                self.processed_files.append({
                    'original': file_path.name,
                    'base_name': base_name,
                    'size': file_path.stat().st_size,
                    'timestamp': datetime.now().isoformat()
                })

            return success

        except Exception as e:
            self.log_message(f"处理MATLAB文件失败 {file_path.name}: {e}", "ERROR")
            return False

    def validate_workflow_data(self, data: Dict) -> bool:
        """验证工作流数据结构的完整性"""
        required_fields = ['nodes', 'edges']

        for field in required_fields:
            if field not in data:
                self.log_message(f"缺少必需字段: {field}", "WARNING")
                return False

        # 检查节点和边的基本结构
        if not isinstance(data['nodes'], list):
            self.log_message("nodes 字段应该是列表", "WARNING")
            return False

        if not isinstance(data['edges'], list):
            self.log_message("edges 字段应该是列表", "WARNING")
            return False

        return True
    
    def create_summary_file(self, data, json_file, summary_file):
        """创建摘要文件"""
        try:
            summary = {
                "文件信息": {
                    "原文件名": json_file.name,
                    "文件大小": f"{json_file.stat().st_size / 1024:.1f} KB",
                    "转换时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "数据结构": {
                    "edges": {
                        "数量": len(data.get('edges', [])),
                        "描述": "工作流节点之间的连接关系"
                    },
                    "nodes": {
                        "数量": len(data.get('nodes', [])),
                        "描述": "工作流中的节点"
                    },
                    "versions": {
                        "内容": data.get('versions', {}),
                        "描述": "版本信息"
                    }
                },
                "使用说明": {
                    "1": "这是一个工作流配置文件",
                    "2": "edges 定义了节点之间的连接关系",
                    "3": "nodes 包含了每个节点的详细信息",
                    "4": "可以直接用任何JSON编辑器打开查看"
                }
            }
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.log_message(f"创建摘要文件失败: {e}")
    
    def batch_convert(self):
        """批量转换文件，支持进度显示和错误恢复"""
        self.start_time = time.time()

        print("🚀 优化版工作流批量转换器")
        print("=" * 60)
        self.log_message("开始批量转换工作流文件...")

        # 查找所有可能的输入文件
        input_files = []
        patterns = ['*.zip', '*.json', '*.mat']

        print("🔍 扫描输入文件...")
        for pattern in patterns:
            found_files = list(self.input_dir.glob(pattern))
            input_files.extend(found_files)
            if found_files:
                print(f"  找到 {len(found_files)} 个 {pattern} 文件")

        if not input_files:
            print("❌ 未找到任何可处理的文件")
            print(f"请确保在目录 {self.input_dir.absolute()} 中有以下格式的文件:")
            for pattern in patterns:
                print(f"  - {pattern}")
            return

        print(f"\n📊 总计找到 {len(input_files)} 个文件待处理")
        print(f"📁 输入目录: {self.input_dir.absolute()}")
        print(f"📁 输出目录: {self.output_dir.absolute()}")
        print(f"📄 输出格式: {self.output_format}")
        print()

        # 显示文件列表
        print("📋 待处理文件列表:")
        for i, file_path in enumerate(input_files, 1):
            size_mb = file_path.stat().st_size / 1024 / 1024
            print(f"  {i:2d}. {file_path.name} ({size_mb:.1f}MB)")
        print()

        # 确认处理
        response = input("是否开始处理？(Y/n): ")
        if response.lower() == 'n':
            print("❌ 用户取消操作")
            return

        # 创建进度条
        progress = ProgressBar(len(input_files))

        # 处理每个文件
        for i, file_path in enumerate(input_files):
            progress.update(i, f"处理中: {file_path.name}")

            try:
                if self.process_single_file(file_path):
                    self.success_count += 1
                else:
                    self.failed_count += 1
            except KeyboardInterrupt:
                print("\n\n⚠️ 用户中断操作")
                break
            except Exception as e:
                self.log_message(f"处理文件时发生未预期错误 {file_path.name}: {e}", "ERROR")
                self.failed_count += 1

        progress.finish("处理完成")

        # 生成处理报告
        self.generate_report()
    
    def generate_report(self):
        """生成详细的处理报告"""
        end_time = time.time()
        duration = end_time - (self.start_time or end_time)

        report = {
            "处理信息": {
                "开始时间": datetime.fromtimestamp(self.start_time or end_time).strftime("%Y-%m-%d %H:%M:%S"),
                "结束时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "处理时长": f"{duration:.2f} 秒",
                "输入目录": str(self.input_dir.absolute()),
                "输出目录": str(self.output_dir.absolute()),
                "输出格式": self.output_format
            },
            "处理统计": {
                "成功": self.success_count,
                "失败": self.failed_count,
                "总计": self.success_count + self.failed_count,
                "成功率": f"{(self.success_count / max(1, self.success_count + self.failed_count) * 100):.1f}%"
            },
            "处理详情": self.processed_files,
            "输出文件": {
                "JSON文件": [],
                "TXT文件": [],
                "摘要文件": []
            }
        }

        # 统计输出文件
        if self.output_format in ["json", "both"]:
            json_dir = self.output_dir / "json"
            if json_dir.exists():
                for json_file in json_dir.glob("*_workflow.json"):
                    report["输出文件"]["JSON文件"].append({
                        "文件名": json_file.name,
                        "大小": f"{json_file.stat().st_size / 1024:.1f} KB",
                        "路径": str(json_file.relative_to(self.output_dir))
                    })

                for summary_file in json_dir.glob("*_summary.json"):
                    report["输出文件"]["摘要文件"].append({
                        "文件名": summary_file.name,
                        "大小": f"{summary_file.stat().st_size / 1024:.1f} KB",
                        "路径": str(summary_file.relative_to(self.output_dir))
                    })

        if self.output_format in ["txt", "both"]:
            txt_dir = self.output_dir / "txt"
            if txt_dir.exists():
                for txt_file in txt_dir.glob("*_workflow.txt"):
                    report["输出文件"]["TXT文件"].append({
                        "文件名": txt_file.name,
                        "大小": f"{txt_file.stat().st_size / 1024:.1f} KB",
                        "路径": str(txt_file.relative_to(self.output_dir))
                    })

        # 保存报告
        report_file = self.output_dir / "conversion_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        # 显示结果摘要
        print("\n" + "=" * 60)
        print("🎉 转换完成！")
        print("=" * 60)
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.failed_count}")
        print(f"📊 成功率: {report['处理统计']['成功率']}")
        print(f"⏱️ 处理时长: {report['处理信息']['处理时长']}")
        print(f"📁 输出目录: {self.output_dir.absolute()}")
        print(f"📋 详细报告: {report_file.name}")

        # 显示输出文件统计
        total_json = len(report["输出文件"]["JSON文件"])
        total_txt = len(report["输出文件"]["TXT文件"])
        total_summary = len(report["输出文件"]["摘要文件"])

        if total_json > 0:
            print(f"📄 JSON文件: {total_json} 个")
        if total_txt > 0:
            print(f"📝 TXT文件: {total_txt} 个")
        if total_summary > 0:
            print(f"📋 摘要文件: {total_summary} 个")

        self.log_message(f"处理完成！成功: {self.success_count}, 失败: {self.failed_count}", "SUCCESS")
        self.log_message(f"详细报告已保存到: {report_file}", "INFO")

def main():
    parser = argparse.ArgumentParser(
        description='优化版批量工作流文件转换器 - 支持JSON和TXT格式输出',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 batch_workflow_converter.py                    # 转换当前目录的文件为JSON和TXT格式
  python3 batch_workflow_converter.py -f json           # 只输出JSON格式
  python3 batch_workflow_converter.py -f txt            # 只输出TXT格式
  python3 batch_workflow_converter.py -i /path/to/input # 指定输入目录
  python3 batch_workflow_converter.py -o /path/to/output # 指定输出目录

支持的文件格式:
  - ZIP文件 (包含工作流的压缩包)
  - MAT文件 (MATLAB格式的工作流文件)
  - JSON文件 (直接的工作流配置文件)
        """
    )

    parser.add_argument('--input', '-i', default='.',
                       help='输入目录 (默认: 当前目录)')
    parser.add_argument('--output', '-o', default='converted_workflows',
                       help='输出目录 (默认: converted_workflows)')
    parser.add_argument('--format', '-f', choices=['json', 'txt', 'both'], default='both',
                       help='输出格式 (默认: both - 同时生成JSON和TXT)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细日志信息')

    args = parser.parse_args()

    try:
        # 创建转换器并执行批量转换
        converter = WorkflowConverter(args.input, args.output, args.format)
        converter.batch_convert()

    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()