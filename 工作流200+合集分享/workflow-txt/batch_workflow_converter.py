#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量工作流文件转换器
将多个工作流ZIP文件转换为可读的JSON文件
"""

import os
import json
import zipfile
import shutil
import re
from pathlib import Path
from datetime import datetime
import argparse

class WorkflowConverter:
    def __init__(self, input_dir=".", output_dir="converted_workflows"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 创建日志文件
        self.log_file = self.output_dir / "conversion_log.txt"
        self.success_count = 0
        self.failed_count = 0
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        print(log_entry)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    
    def extract_json_from_file(self, file_path):
        """从文件中提取JSON数据"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 尝试找到JSON开始的位置
            content_str = content.decode('utf-8', errors='ignore')
            json_start = content_str.find('{')
            
            if json_start == -1:
                return None
            
            # 提取JSON部分
            json_content = content_str[json_start:]
            
            # 尝试解析JSON
            try:
                data = json.loads(json_content)
                return data
            except json.JSONDecodeError:
                # 尝试修复JSON
                fixed_json = self.fix_json_content(json_content)
                if fixed_json:
                    return json.loads(fixed_json)
                return None
                
        except Exception as e:
            self.log_message(f"提取JSON失败: {e}")
            return None
    
    def fix_json_content(self, json_content):
        """尝试修复JSON内容"""
        try:
            # 移除可能的控制字符
            json_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_content)
            
            # 尝试找到完整的JSON对象
            brace_count = 0
            end_pos = 0
            
            for i, char in enumerate(json_content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_pos = i + 1
                        break
            
            if end_pos > 0:
                return json_content[:end_pos]
            
            return None
            
        except Exception as e:
            self.log_message(f"修复JSON失败: {e}")
            return None
    
    def process_single_file(self, file_path):
        """处理单个文件"""
        file_name = file_path.name
        self.log_message(f"开始处理: {file_name}")
        
        try:
            # 检查文件类型
            if file_path.suffix.lower() == '.zip':
                # 处理ZIP文件
                return self.process_zip_file(file_path)
            elif file_path.suffix.lower() in ['.json', '.mat']:
                # 直接处理文件
                return self.process_direct_file(file_path)
            else:
                self.log_message(f"跳过不支持的文件类型: {file_name}")
                return False
                
        except Exception as e:
            self.log_message(f"处理文件失败 {file_name}: {e}")
            return False
    
    def process_zip_file(self, zip_path):
        """处理ZIP文件"""
        try:
            # 创建临时目录
            temp_dir = self.output_dir / f"temp_{zip_path.stem}"
            temp_dir.mkdir(exist_ok=True)
            
            # 解压ZIP文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 查找MATLAB文件
            matlab_files = list(temp_dir.glob("*.zip"))  # 通常是嵌套的ZIP
            if not matlab_files:
                matlab_files = list(temp_dir.glob("*.mat"))
            
            if not matlab_files:
                self.log_message(f"在ZIP中未找到MATLAB文件: {zip_path.name}")
                shutil.rmtree(temp_dir)
                return False
            
            # 处理找到的文件
            success = False
            for matlab_file in matlab_files:
                if self.process_matlab_file(matlab_file, zip_path.stem):
                    success = True
                    break
            
            # 清理临时目录
            shutil.rmtree(temp_dir)
            return success
            
        except Exception as e:
            self.log_message(f"处理ZIP文件失败 {zip_path.name}: {e}")
            return False
    
    def process_direct_file(self, file_path):
        """直接处理文件"""
        return self.process_matlab_file(file_path, file_path.stem)
    
    def process_matlab_file(self, file_path, base_name):
        """处理MATLAB文件"""
        try:
            # 提取JSON数据
            data = self.extract_json_from_file(file_path)
            if not data:
                self.log_message(f"无法提取JSON数据: {file_path.name}")
                return False
            
            # 生成输出文件名
            output_file = self.output_dir / f"{base_name}_workflow.json"
            summary_file = self.output_dir / f"{base_name}_summary.json"
            
            # 保存JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 创建摘要文件
            self.create_summary_file(data, output_file, summary_file)
            
            self.log_message(f"✅ 成功转换: {output_file.name}")
            return True
            
        except Exception as e:
            self.log_message(f"处理MATLAB文件失败 {file_path.name}: {e}")
            return False
    
    def create_summary_file(self, data, json_file, summary_file):
        """创建摘要文件"""
        try:
            summary = {
                "文件信息": {
                    "原文件名": json_file.name,
                    "文件大小": f"{json_file.stat().st_size / 1024:.1f} KB",
                    "转换时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                },
                "数据结构": {
                    "edges": {
                        "数量": len(data.get('edges', [])),
                        "描述": "工作流节点之间的连接关系"
                    },
                    "nodes": {
                        "数量": len(data.get('nodes', [])),
                        "描述": "工作流中的节点"
                    },
                    "versions": {
                        "内容": data.get('versions', {}),
                        "描述": "版本信息"
                    }
                },
                "使用说明": {
                    "1": "这是一个工作流配置文件",
                    "2": "edges 定义了节点之间的连接关系",
                    "3": "nodes 包含了每个节点的详细信息",
                    "4": "可以直接用任何JSON编辑器打开查看"
                }
            }
            
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.log_message(f"创建摘要文件失败: {e}")
    
    def batch_convert(self):
        """批量转换文件"""
        self.log_message("开始批量转换工作流文件...")
        
        # 查找所有可能的输入文件
        input_files = []
        for pattern in ['*.zip', '*.json', '*.mat']:
            input_files.extend(self.input_dir.glob(pattern))
        
        if not input_files:
            self.log_message("未找到任何可处理的文件")
            return
        
        self.log_message(f"找到 {len(input_files)} 个文件待处理")
        
        # 处理每个文件
        for file_path in input_files:
            if self.process_single_file(file_path):
                self.success_count += 1
            else:
                self.failed_count += 1
        
        # 生成处理报告
        self.generate_report()
    
    def generate_report(self):
        """生成处理报告"""
        report = {
            "处理时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "输入目录": str(self.input_dir),
            "输出目录": str(self.output_dir),
            "处理统计": {
                "成功": self.success_count,
                "失败": self.failed_count,
                "总计": self.success_count + self.failed_count
            },
            "输出文件": []
        }
        
        # 列出输出文件
        for json_file in self.output_dir.glob("*_workflow.json"):
            report["输出文件"].append({
                "文件名": json_file.name,
                "大小": f"{json_file.stat().st_size / 1024:.1f} KB"
            })
        
        # 保存报告
        report_file = self.output_dir / "conversion_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.log_message(f"处理完成！成功: {self.success_count}, 失败: {self.failed_count}")
        self.log_message(f"详细报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='批量转换工作流文件为JSON格式')
    parser.add_argument('--input', '-i', default='.', help='输入目录 (默认: 当前目录)')
    parser.add_argument('--output', '-o', default='converted_workflows', help='输出目录 (默认: converted_workflows)')
    
    args = parser.parse_args()
    
    # 创建转换器并执行批量转换
    converter = WorkflowConverter(args.input, args.output)
    converter.batch_convert()

if __name__ == "__main__":
    main() 