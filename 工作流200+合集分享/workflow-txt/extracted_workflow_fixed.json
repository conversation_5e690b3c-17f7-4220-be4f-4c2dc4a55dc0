{"edges": [{"sourceNodeID": "100001", "sourcePortID": "", "targetNodeID": "104474"}, {"sourceNodeID": "196263", "sourcePortID": "", "targetNodeID": "900001"}, {"sourceNodeID": "438515", "sourcePortID": "", "targetNodeID": "133604"}, {"sourceNodeID": "133604", "sourcePortID": "", "targetNodeID": "199678"}, {"sourceNodeID": "199678", "sourcePortID": "", "targetNodeID": "176835"}, {"sourceNodeID": "157066", "sourcePortID": "", "targetNodeID": "120765"}, {"sourceNodeID": "120765", "sourcePortID": "", "targetNodeID": "196263"}, {"sourceNodeID": "895301", "sourcePortID": "", "targetNodeID": "163434"}, {"sourceNodeID": "163434", "sourcePortID": "", "targetNodeID": "167928"}, {"sourceNodeID": "176835", "sourcePortID": "", "targetNodeID": "157066"}, {"sourceNodeID": "139227", "sourcePortID": "", "targetNodeID": "194925"}, {"sourceNodeID": "194925", "sourcePortID": "batch-output", "targetNodeID": "154520"}, {"sourceNodeID": "167928", "sourcePortID": "", "targetNodeID": "438515"}, {"sourceNodeID": "553483", "sourcePortID": "", "targetNodeID": "180549"}, {"sourceNodeID": "180549", "sourcePortID": "", "targetNodeID": "141368"}, {"sourceNodeID": "141368", "sourcePortID": "", "targetNodeID": "069541"}, {"sourceNodeID": "149915", "sourcePortID": "", "targetNodeID": "825433"}, {"sourceNodeID": "825433", "sourcePortID": "", "targetNodeID": "461472"}, {"sourceNodeID": "069541", "sourcePortID": "", "targetNodeID": "895301"}, {"sourceNodeID": "117382", "sourcePortID": "", "targetNodeID": "149915"}, {"sourceNodeID": "156181", "sourcePortID": "", "targetNodeID": "177620"}, {"sourceNodeID": "177620", "sourcePortID": "", "targetNodeID": "849106"}, {"sourceNodeID": "177027", "sourcePortID": "", "targetNodeID": "156181"}, {"sourceNodeID": "461472", "sourcePortID": "", "targetNodeID": "129955"}, {"sourceNodeID": "129955", "sourcePortID": "", "targetNodeID": "139227"}, {"sourceNodeID": "047111", "sourcePortID": "", "targetNodeID": "117382"}, {"sourceNodeID": "473153", "sourcePortID": "", "targetNodeID": "117382"}, {"sourceNodeID": "154520", "sourcePortID": "batch-output", "targetNodeID": "130348"}, {"sourceNodeID": "130348", "sourcePortID": "", "targetNodeID": "196018"}, {"sourceNodeID": "154520", "sourcePortID": "batch-output", "targetNodeID": "141842"}, {"sourceNodeID": "141842", "sourcePortID": "", "targetNodeID": "196018"}, {"sourceNodeID": "849106", "sourcePortID": "", "targetNodeID": "547995"}, {"sourceNodeID": "090296", "sourcePortID": "", "targetNodeID": "553483"}, {"sourceNodeID": "104474", "sourcePortID": "true", "targetNodeID": "105836"}, {"sourceNodeID": "105836", "sourcePortID": "", "targetNodeID": "174010"}, {"sourceNodeID": "104474", "sourcePortID": "false", "targetNodeID": "178546"}, {"sourceNodeID": "178546", "sourcePortID": "", "targetNodeID": "174010"}, {"sourceNodeID": "174010", "sourcePortID": "", "targetNodeID": "111131"}, {"sourceNodeID": "154520", "sourcePortID": "batch-output", "targetNodeID": "314185"}, {"sourceNodeID": "314185", "sourcePortID": "", "targetNodeID": "196018"}, {"sourceNodeID": "196018", "sourcePortID": "", "targetNodeID": "090296"}, {"sourceNodeID": "547995", "sourcePortID": "", "targetNodeID": "047111"}, {"sourceNodeID": "547995", "sourcePortID": "", "targetNodeID": "473153"}, {"sourceNodeID": "111131", "sourcePortID": "", "targetNodeID": "177027"}], "nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "title", "required": false, "type": "string"}, {"name": "content", "required": false, "type": "string"}, {"name": "audio", "required": false, "type": "string"}, {"name": "bg_audio", "required": false, "type": "string"}, {"assistType": 2, "name": "logo", "required": false, "type": "string"}], "trigger_parameters": []}, "edges": null, "id": "100001", "meta": {"position": {"x": -1509.8453141033751, "y": -3006.825087686811}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "196263", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 2888.117274112133, "y": -397.2402576703449}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833883688", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_images", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "438515", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": {"blockID": "090296", "name": "images", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "image_infos"}, {"input": {"type": "float", "value": {"content": 0.4, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_x"}, {"input": {"type": "float", "value": {"content": 0.4, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_y"}, {"input": {"type": "float", "value": {"content": 600, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "transform_x"}, {"input": {"type": "float", "value": {"content": -210, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "transform_y"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_images", "title": "add_images右边图片"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "image_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_infos", "required": false, "schema": {"schema": [{"name": "end", "required": false, "type": "integer"}, {"name": "id", "required": false, "type": "string"}, {"name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "133604", "meta": {"position": {"x": 11.704162105176753, "y": -458.8707193925587}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833850920", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_captions", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "149915", "name": "result", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "captions"}, {"input": {"type": "string", "value": {"content": {"blockID": "133604", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": "1", "type": "literal"}}, "name": "alignment"}, {"input": {"type": "string", "value": {"content": "5", "type": "literal"}}, "name": "font_size"}, {"input": {"type": "string", "value": {"content": "#000000", "type": "literal"}}, "name": "text_color"}, {"input": {"type": "string", "value": {"content": "-780", "type": "literal"}}, "name": "transform_y"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加字幕", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_captions", "title": "add_captions字幕"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_infos", "required": false, "schema": {"schema": [{"name": "end", "required": false, "type": "integer"}, {"name": "id", "required": false, "type": "string"}, {"name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"name": "text_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "199678", "meta": {"position": {"x": 411.0036046451233, "y": -466.56493462475623}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833834536", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_audios", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]", "input": {}, "name": "audio_infos", "required": true, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "141368", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "audio_infos"}, {"input": {"type": "string", "value": {"content": {"blockID": "157066", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加音频", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_audios", "title": "add_audios配音"}, "outputs": [{"name": "audio_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "draft_url", "required": false, "type": "string"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "120765", "meta": {"position": {"x": 1791.0036046451214, "y": -466.56493462475623}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837955684515874", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "create_draft", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "高", "input": {}, "name": "height", "required": false, "type": "integer"}, {"description": "关联创作者", "input": {}, "name": "user_id", "required": false, "type": "integer"}, {"description": "宽", "input": {}, "name": "width", "required": false, "type": "integer"}], "inputParameters": [{"input": {"type": "string", "value": {"content": "1080", "type": "literal"}}, "name": "height"}, {"input": {"type": "integer", "value": {"content": 19521, "rawMeta": {"type": 2}, "type": "literal"}}, "name": "user_id"}, {"input": {"type": "string", "value": {"content": "1920", "type": "literal"}}, "name": "width"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "创建草稿", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:create_draft", "title": "创建草稿"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "tip_url", "required": false, "type": "string"}]}, "edges": null, "id": "163434", "meta": {"position": {"x": 2286.472632540351, "y": -1149.561587288664}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837955684548642", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "save_draft", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "用户ID。如果填写了这个ID，新用户产生的月费就会按照比例归属到这个账号下。", "input": {}, "name": "user_id", "required": false, "type": "integer"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "120765", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "integer", "value": {"content": 19521, "rawMeta": {"type": 2}, "type": "literal"}}, "name": "user_id"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "保存草稿\n", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:save_draft", "title": "save_draft"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "message", "required": false, "type": "string"}]}, "edges": null, "id": "196263", "meta": {"position": {"x": 2186.4726325403544, "y": -264.7243986193005}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7466630948491231268", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_effects", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "特效数组内容，eg:[{\"effect_title\":\"金粉闪闪\",\"end\":5000000,\"start\":0}]", "input": {}, "name": "effect_infos", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "176835", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": {"blockID": "895301", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "effect_infos"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "添加特效", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_effects", "title": "add_effects特效"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "effect_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "157066", "meta": {"position": {"x": 1331.0036046451214, "y": -466.56493462475623}}, "type": "4"}, {"blocks": [{"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "194925", "name": "content.output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "promot"}], "modelSetting": {"custom_ratio": {"height": 1024, "width": 576}, "ddim_steps": 28, "model": 8}, "prompt": {"negative_prompt": "细胳膊、细腿、穿衣服、表情细节、真实人物、复杂背景、白色火柴人，大头，人物变形，多脚，多手，三个脚，三个手，描边，颜色，纯黑色背景，渐变，缺乏四肢，不干净的画面，多边形，立体，3d,彩色，五颜六色，描边，颜色，纯黑色背景，渐变，缺乏四肢，不干净的画面，多边形，立体，3d，彩色，五颜六色，真实人物，真实物品，text,background,shadow,blur,l\now quality,pixelated,distorted,artifacts,incomplete,unfinished,missing parts,broken,\ndisfigured,glitch,unnatural,unrealistic,disproportional,out of place,floating objects,in\nconsistent lighting,incorrect perspective,distorted anatomy,wrong proportions,incorre\nct details,wrong colors,incorrect anatomy,overlapping elements,misplaced objects,inc\nonsistent details,wrong facial expressions,unbalanced composition,strange textures,de\nformed,asymmetrical faces,3D render,overly detailed,hyper-realistic,photorealistic,gra\niny,noisy,dark,creepy,horror,grotesque,abstract,mutated,extra limbs,extra fingers,d\neformed faces,incorrect clothing,misplaced accessories,wrong facial expressions,unrea\nlistic colors,extra noses,wrong animal anatomy\n，Thin arms, thin legs, slim lines, weakened structural sense, children's cartoon style, lightweight lines, thin hands and feet, cartoon style, cartoon proportion", "prompt": "黑白风格，剪影，矢量图插画风格，黑色火柴人，最佳品质，超精细，杰作，4k，丰富的细节，{{promot}}，白色背景，黑白风格，圆头，简洁画面，无毛边，无羽化。"}, "references": [], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 60000}}, "nodeMeta": {"description": "通过文字描述/添加参考图生成图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "mainColor": "#FF4DC3", "subTitle": "图像生成", "title": "第一次图像生成"}, "outputs": [{"assistType": 2, "name": "data", "type": "string"}, {"name": "msg", "type": "string"}]}, "edges": null, "id": "76198", "meta": {"position": {"x": 180, "y": 126.05000000000003}}, "type": "16"}, {"blocks": [], "data": {"inputs": {"branches": [{"condition": {"conditions": [{"left": {"input": {"assistType": 2, "type": "string", "value": {"content": {"blockID": "76198", "name": "data", "source": "block-output"}, "type": "ref"}}}, "operator": 9}], "logic": 2}}]}, "nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "重新生成选择"}}, "edges": null, "id": "150346", "meta": {"position": {"x": 640, "y": 139.15000000000003}}, "type": "8"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "194925", "name": "content.output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "promot"}], "modelSetting": {"custom_ratio": {"height": 1024, "width": 1024}, "ddim_steps": 28, "model": 8}, "prompt": {"negative_prompt": "边框，线条人，黑色背景，细手臂，白色描边", "prompt": "{{promot}}"}, "references": [], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 60000}}, "nodeMeta": {"description": "通过文字描述/添加参考图生成图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-ImageGeneration-v2.jpg", "mainColor": "#FF4DC3", "subTitle": "图像生成", "title": "第二次重新生成"}, "outputs": [{"assistType": 2, "name": "data", "type": "string"}, {"name": "msg", "type": "string"}]}, "edges": null, "id": "069016", "meta": {"position": {"x": 1100, "y": 1.4210854715202004e-14}}, "type": "16"}, {"blocks": [], "data": {"inputs": {"mergeGroups": [{"name": "Group1", "variables": [{"assistType": 2, "type": "string", "value": {"content": {"blockID": "187304", "name": "data", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}, {"assistType": 2, "type": "string", "value": {"content": {"blockID": "069016", "name": "data", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}]}]}, "nodeMeta": {"description": "对多个分支的输出进行聚合处理", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/VariableMerge-icon.jpg", "mainColor": "#00B2B2", "subTitle": "变量聚合", "title": "输出一条"}, "outputs": [{"assistType": 2, "name": "Group1", "type": "string"}]}, "edges": null, "id": "136347", "meta": {"position": {"x": 1560, "y": 151.05}}, "type": "32"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7438919188246429731", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "cutout", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7438919188246413347", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "抠图", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"input": {"assistType": 2, "type": "string", "value": {"content": {"blockID": "76198", "name": "data", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}, "name": "url"}, {"input": {"type": "string", "value": {"content": "0", "type": "literal"}}, "name": "only_mask"}, {"input": {"type": "string", "value": {"content": "0", "type": "literal"}}, "name": "output_mode"}], "settingOnError": {"dataOnErr": "{\n    \"data\": \"\",\n    \"mask\": \"\",\n    \"msg\": \"\"\n}", "processType": 2, "retryTimes": 0, "switch": true, "timeoutMs": 180000}}, "nodeMeta": {"description": "保留图片前景主体，输出透明背景(.png)", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-plugin-cutout-v2.jpg", "subtitle": "抠图:cutout", "title": "cutout"}, "outputs": [{"assistType": 2, "description": "透明背景图，在输出模式为透明背景时生效", "name": "data", "required": false, "type": "string"}, {"description": "抠图区域蒙板矢量图，在输出模式为蒙版矢量图时生效", "name": "mask", "required": false, "type": "string"}, {"name": "msg", "required": false, "type": "string"}, {"name": "errorBody", "readonly": true, "schema": [{"name": "errorMessage", "readonly": true, "type": "string"}, {"name": "errorCode", "readonly": true, "type": "string"}], "type": "object"}, {"name": "isSuccess", "readonly": true, "type": "boolean"}]}, "edges": null, "id": "187304", "meta": {"position": {"x": 1100, "y": 266.1}}, "type": "4"}], "data": {"inputs": {"batchSize": {"type": "integer", "value": {"content": "100", "type": "literal"}}, "concurrentSize": {"type": "integer", "value": {"content": 3, "rawMeta": {"type": 2}, "type": "literal"}}, "inputParameters": [{"input": {"schema": {"schema": [{"name": "output", "type": "string"}], "type": "object"}, "type": "list", "value": {"content": {"blockID": "139227", "name": "outputList", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "content"}]}, "nodeMeta": {"description": "通过设定批量运行次数和逻辑，运行批处理体内的任务", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Batch-v2.jpg", "mainColor": "#00B2B2", "subTitle": "批处理", "title": "画面内容"}, "outputs": [{"input": {"assistType": 2, "schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "136347", "name": "Group1", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}, "name": "imagelist"}]}, "edges": [{"sourceNodeID": "194925", "sourcePortID": "batch-function-inline-output", "targetNodeID": "76198"}, {"sourceNodeID": "76198", "sourcePortID": "", "targetNodeID": "150346"}, {"sourceNodeID": "150346", "sourcePortID": "true", "targetNodeID": "069016"}, {"sourceNodeID": "150346", "sourcePortID": "false", "targetNodeID": "187304"}, {"sourceNodeID": "069016", "sourcePortID": "", "targetNodeID": "136347"}, {"sourceNodeID": "187304", "sourcePortID": "", "targetNodeID": "136347"}, {"sourceNodeID": "136347", "sourcePortID": "", "targetNodeID": "194925", "targetPortID": "batch-function-inline-input"}], "id": "194925", "meta": {"canvasPosition": {"x": -1217.3335913545898, "y": -1762.7527201192527}, "position": {"x": 422.6664086454121, "y": -2056.852720119253}}, "type": "28"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833883688", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_images", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "163434", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": {"blockID": "069541", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "image_infos"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_images", "title": "add_images背景图"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "image_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_infos", "required": false, "schema": {"schema": [{"name": "end", "required": false, "type": "integer"}, {"name": "id", "required": false, "type": "string"}, {"name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "167928", "meta": {"position": {"x": 2746.472632540351, "y": -1137.561587288664}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"canvasSchema": "{\"version\":\"6.0.0-rc2\",\"width\":1920,\"height\":1080,\"backgroundColor\":\"#ffffffff\",\"customVariableRefs\":[{\"variableId\":\"qtz3JL6CEltwoURZNTDNi\",\"objectId\":\"wubitD4PbOCLGd4Sjggr2\",\"variableName\":\"title\"},{\"variableId\":\"Bxu7jcrIJ6z8obvCBtHdT\",\"objectId\":\"7wGZRh03tbT5XkozrFT3t\",\"variableName\":\"logo\"}],\"objects\":[{\"subTargetCheck\":false,\"interactive\":false,\"width\":1927.8175,\"height\":1081.1726,\"backgroundColor\":\"\",\"padding\":0,\"customFixedHeight\":1081.172638436482,\"customId\":\"2EJJxS_GYsRI6UyARPWnh\",\"customType\":\"img\",\"type\":\"Group\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-81.4602,\"top\":1062.5092,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"clipPath\":{\"rx\":0,\"ry\":0,\"width\":1927.8175,\"height\":1081.1726,\"backgroundColor\":\"\",\"padding\":0,\"type\":\"Rect\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-963.9088,\"top\":-540.5863,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":1,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"inverted\":false,\"absolutePositioned\":false},\"layoutManager\":{\"type\":\"layoutManager\",\"strategy\":\"fit-content\"},\"objects\":[{\"cropX\":0,\"cropY\":0,\"width\":1920,\"height\":1080,\"backgroundColor\":\"\",\"padding\":0,\"customFixedType\":\"auto\",\"type\":\"Image\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-961.9312,\"top\":-541.0863,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1.002,\"scaleY\":1.002,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"src\":\"https://p9-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/4b98d0b317da49b18415af30110241a9.png~tplv-mdko3gqilj-image.image?rk3s=81d4c505&x-expires=1772173443&x-signature=Lo6PcwOu%2BYaaQUD%2Brh343rCa2bw%3D\",\"crossOrigin\":null,\"filters\":[]},{\"rx\":0,\"ry\":0,\"width\":1923.8624,\"height\":1081.1726,\"backgroundColor\":\"\",\"padding\":0,\"type\":\"Rect\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-961.9312,\"top\":-540.5863,\"fill\":\"#00000000\",\"stroke\":\"#000000ff\",\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0}]},{\"fontSize\":48,\"fontWeight\":\"normal\",\"fontFamily\":\"字语咏乐体\",\"fontStyle\":\"normal\",\"lineHeight\":1.2,\"text\":\"心理知识分享\\n无不良引导\",\"charSpacing\":0,\"textAlign\":\"right\",\"styles\":[],\"pathStartOffset\":0,\"pathSide\":\"left\",\"pathAlign\":\"baseline\",\"underline\":false,\"overline\":false,\"linethrough\":false,\"textBackgroundColor\":\"\",\"direction\":\"ltr\",\"width\":288,\"height\":119.328,\"editable\":true,\"backgroundColor\":\"\",\"padding\":0,\"customId\":\"CYFvams3d8xObzzSeAi6r\",\"customType\":\"inline_text\",\"type\":\"IText\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":1558.3573,\"top\":43.6816,\"fill\":\"#000000ff\",\"stroke\":\"#000000ff\",\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0},{\"fontSize\":32,\"fontWeight\":\"normal\",\"fontFamily\":\"字语咏乐体\",\"fontStyle\":\"normal\",\"lineHeight\":1.2,\"text\":\"认知提升·自我觉醒·强大内核\",\"charSpacing\":0,\"textAlign\":\"left\",\"styles\":[],\"pathStartOffset\":0,\"pathSide\":\"left\",\"pathAlign\":\"baseline\",\"underline\":false,\"overline\":false,\"linethrough\":false,\"textBackgroundColor\":\"\",\"direction\":\"ltr\",\"minWidth\":20,\"splitByGrapheme\":true,\"width\":468.3111,\"height\":109.058,\"editable\":true,\"backgroundColor\":\"\",\"padding\":8,\"customFixedHeight\":109.05798433647922,\"customId\":\"wubitD4PbOCLGd4Sjggr2\",\"customType\":\"block_text\",\"type\":\"Textbox\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":116.0914,\"top\":53.9516,\"fill\":\"#000000ff\",\"stroke\":\"#000000ff\",\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"clipPath\":{\"rx\":0,\"ry\":0,\"width\":484.3111,\"height\":125.058,\"backgroundColor\":\"\",\"padding\":0,\"type\":\"Rect\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-242.1556,\"top\":-62.529,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":1,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"inverted\":false,\"absolutePositioned\":false}},{\"subTargetCheck\":false,\"interactive\":false,\"width\":58.2387,\"height\":52.5643,\"backgroundColor\":\"\",\"padding\":0,\"customFixedHeight\":52.56434596976118,\"customId\":\"7wGZRh03tbT5XkozrFT3t\",\"customType\":\"img\",\"type\":\"Group\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":33.7199,\"top\":50.7813,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"clipPath\":{\"rx\":0,\"ry\":0,\"width\":58.2387,\"height\":52.5643,\"backgroundColor\":\"\",\"padding\":0,\"type\":\"Rect\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-29.1194,\"top\":-26.2822,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":1,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"inverted\":false,\"absolutePositioned\":false},\"layoutManager\":{\"type\":\"layoutManager\",\"strategy\":\"fit-content\"},\"objects\":[{\"cropX\":0,\"cropY\":0,\"width\":400,\"height\":400,\"editable\":false,\"backgroundColor\":\"\",\"padding\":0,\"customFixedType\":\"auto\",\"type\":\"Image\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-26.7821,\"top\":-26.7821,\"fill\":\"rgb(0,0,0)\",\"stroke\":null,\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":0.1339,\"scaleY\":0.1339,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0,\"src\":\"https://lf-coze-web-cdn.coze.cn/obj/eden-cn/lm-lgvj/ljhwZthlaukjlkulzlp//workflow/fabric-canvas/img-placeholder.png\",\"crossOrigin\":null,\"filters\":[]},{\"rx\":0,\"ry\":0,\"width\":53.5643,\"height\":52.5643,\"backgroundColor\":\"\",\"padding\":0,\"type\":\"Rect\",\"version\":\"6.0.0-rc2\",\"originX\":\"left\",\"originY\":\"top\",\"left\":-26.7821,\"top\":-26.2822,\"fill\":\"#00000000\",\"stroke\":\"#000000ff\",\"strokeWidth\":0,\"strokeDashArray\":null,\"strokeLineCap\":\"butt\",\"strokeDashOffset\":0,\"strokeLineJoin\":\"miter\",\"strokeUniform\":false,\"strokeMiterLimit\":4,\"scaleX\":1,\"scaleY\":1,\"angle\":0,\"flipX\":false,\"flipY\":false,\"opacity\":1,\"shadow\":null,\"visible\":true,\"fillRule\":\"nonzero\",\"paintFirst\":\"fill\",\"globalCompositeOperation\":\"source-over\",\"skewX\":0,\"skewY\":0}]}],\"background\":\"#ffffffff\"}", "inputParameters": [{"id": "qtz3JL6CEltwoURZNTDNi", "input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "title", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "title"}, {"id": "Bxu7jcrIJ6z8obvCBtHdT", "input": {"assistType": 2, "type": "string", "value": {"content": {"blockID": "100001", "name": "logo", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}, "name": "logo"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 60000}}, "nodeMeta": {"description": "自定义画板排版，支持引用添加文本和图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon_DrawingBoard_v2.jpg", "mainColor": "#FF4DC3", "subTitle": "画板", "title": "背景图"}, "outputs": [{"assistType": 2, "name": "data", "type": "string"}, {"name": "msg", "type": "string"}]}, "edges": null, "id": "180549", "meta": {"position": {"x": 446.4726325403545, "y": -1137.561587288664}}, "type": "23"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833850920", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_captions", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "边框颜色，eg：#fe8a80", "input": {}, "name": "border_color", "required": false, "type": "string"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "input": {}, "name": "font", "required": false, "type": "string"}, {"description": "默认：15", "input": {}, "name": "font_size", "required": false, "type": "integer"}, {"description": "字间距，默认0", "input": {}, "name": "letter_spacing", "required": false, "type": "float"}, {"description": "文字颜色：#ff1837", "input": {}, "name": "text_color", "required": false, "type": "string"}, {"description": "transform_y位置", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "字幕的对齐方式，0左对齐，1 居中对齐，2右对齐", "input": {}, "name": "alignment", "required": false, "type": "integer"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "input": {}, "name": "captions", "required": true, "type": "string"}, {"description": "行间距，默认0", "input": {}, "name": "line_spacing", "required": false, "type": "float"}, {"description": "scale_x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "scale_y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "0 默认。1富文本样式", "input": {}, "name": "style_text", "required": false, "type": "integer"}, {"description": "transform_x位置", "input": {}, "name": "transform_x", "required": false, "type": "float"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "129955", "name": "English", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "captions"}, {"input": {"type": "string", "value": {"content": {"blockID": "199678", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": "1", "type": "literal"}}, "name": "alignment"}, {"input": {"type": "string", "value": {"content": "5", "type": "literal"}}, "name": "font_size"}, {"input": {"type": "float", "value": {"content": 0.6, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_x"}, {"input": {"type": "float", "value": {"content": 0.6, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_y"}, {"input": {"type": "string", "value": {"content": "#000000", "type": "literal"}}, "name": "text_color"}, {"input": {"type": "float", "value": {"content": -950, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "transform_y"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加字幕", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_captions", "title": "add_captions英语字幕_1"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_infos", "required": false, "schema": {"schema": [{"name": "end", "required": false, "type": "integer"}, {"name": "id", "required": false, "type": "string"}, {"name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"name": "text_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "176835", "meta": {"position": {"x": 871.0036046451233, "y": -466.56493462475623}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"concatParams": [{"input": {"type": "string", "value": {"content": "[\n{\"audio_url\": \"{{String6}}\",\"duration\":{{String1}},\"start\":0,\"end\":{{String3}}},\n{\"audio_url\": \"{{String8}}\",\"duration\":{{String1}},\"start\":0,\"end\":{{String3}}}\n]", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "concatResult"}, {"input": {"type": "string", "value": {"content": "，", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "arrayItemConcatChar"}, {"input": {"schema": {"schema": [{"name": "label", "required": true, "type": "string"}, {"name": "value", "required": true, "type": "string"}, {"name": "isDefault", "required": true, "type": "boolean"}], "type": "object"}, "type": "list", "value": {"content": [{"isDefault": true, "label": "换行", "value": "\n"}, {"isDefault": true, "label": "制表符", "value": "\t"}, {"isDefault": true, "label": "句号", "value": "。"}, {"isDefault": true, "label": "逗号", "value": "，"}, {"isDefault": true, "label": "分号", "value": "；"}, {"isDefault": true, "label": "空格", "value": " "}], "type": "literal"}}, "name": "allArrayItemConcatChars"}], "inputParameters": [{"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_s", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String1"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String2"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_start_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String3"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_end_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String4"}, {"input": {"type": "string", "value": {"content": {"blockID": "174010", "name": "Group1", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "String6"}, {"input": {"assistType": 8, "type": "string", "value": {"content": {"blockID": "100001", "name": "bg_audio", "source": "block-output"}, "rawMeta": {"type": 14}, "type": "ref"}}, "name": "String8"}], "method": "concat"}, "nodeMeta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-StrConcat-v2.jpg", "mainColor": "#3071F2", "subTitle": "文本处理", "title": "构建配音和背景音乐"}, "outputs": [{"name": "output", "required": true, "type": "string"}]}, "edges": null, "id": "141368", "meta": {"position": {"x": 906.4726325403545, "y": -1137.561587288664}}, "type": "15"}, {"blocks": [], "data": {"inputs": {"fcParamVar": {"knowledgeFCParam": {}}, "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "547995", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "Content"}], "llmParam": [{"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "1024", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "integer", "value": {"content": "1742989917", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "{{Content}}严格杜绝针对同一个原文字段生成多份译文 ", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "# 角色\n你是一位专业的翻译专家，擅长将中文文本准确流畅地翻译成英文。\n\n## 技能\n### 技能 1: 翻译中文文本\n1. 接收到{{Content}}中文内容后，按照原文案字段数，将每个字段准确翻译成英文，确保每个译文保持对应原文字段的意义、语气和风格。\n2. 在翻译过程中，充分考虑中文的语境和文化内涵，使英文表达既忠实原文又符合英语习惯。禁止针对同一个原文字段生成多份译文。\n3. 如遇中文习语或文化特色内容，采用符合英语表达习惯的译法，保证信息完整性和传达效果。\n\n### 技能 2: 优化英文译文\n1. 在保持准确性的前提下，适当调整每个译文句子结构，使译文更符合英文表达习惯。\n2. 对于多义词或模糊表达，确保每个译文明确、连贯且一致。\n3. 如直译可能导致歧义，采用意译方法，同时确保不改变原意。\n\n## 输出要求\n1. 翻译后的英文文本必须符合英语语法规范，每个译文表达清晰、流畅，并具有良好的可读性。\n2. 准确传达原文每个字段的所有信息，避免随意添加或删减内容。\n3.输出不需要分段，每一段输出一个整体就可以了\n\n## 限制\n1. 仅提供与中文到英文翻译相关的服务，不涉及其他语言对或其他类型的任务。\n2. 输出内容需符合上述输出要求和技能规范，严格杜绝针对同一个原文字段生成多份译文 。 ", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "stableSystemPrompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "canContinue"}], "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 600000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "subTitle": "大模型", "title": "英文翻译"}, "outputs": [{"name": "output", "required": false, "schema": {"type": "string"}, "type": "list"}], "version": "3"}, "edges": null, "id": "825433", "meta": {"position": {"x": 1430.9001816716018, "y": -2618.863693198731}}, "type": "3"}, {"blocks": [], "data": {"inputs": {"concatParams": [{"input": {"type": "string", "value": {"content": "[\n{\"image_url\":\"{{String6}}\",\"width\":1920,\"height\":1080,\"start\":0,\"end\":{{String3}}}\n]", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "concatResult"}, {"input": {"type": "string", "value": {"content": "，", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "arrayItemConcatChar"}, {"input": {"schema": {"schema": [{"name": "label", "required": true, "type": "string"}, {"name": "value", "required": true, "type": "string"}, {"name": "isDefault", "required": true, "type": "boolean"}], "type": "object"}, "type": "list", "value": {"content": [{"isDefault": true, "label": "换行", "value": "\n"}, {"isDefault": true, "label": "制表符", "value": "\t"}, {"isDefault": true, "label": "句号", "value": "。"}, {"isDefault": true, "label": "逗号", "value": "，"}, {"isDefault": true, "label": "分号", "value": "；"}, {"isDefault": true, "label": "空格", "value": " "}], "type": "literal"}}, "name": "allArrayItemConcatChars"}], "inputParameters": [{"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_s", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String1"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String2"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_start_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String3"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_end_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String4"}, {"input": {"assistType": 2, "type": "string", "value": {"content": {"blockID": "180549", "name": "data", "source": "block-output"}, "rawMeta": {"type": 7}, "type": "ref"}}, "name": "String6"}, {"input": {"assistType": 8, "type": "string", "value": {"content": {"blockID": "100001", "name": "audio", "source": "block-output"}, "rawMeta": {"type": 14}, "type": "ref"}}, "name": "String8"}], "method": "concat"}, "nodeMeta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-StrConcat-v2.jpg", "mainColor": "#3071F2", "subTitle": "文本处理", "title": "构建背景图"}, "outputs": [{"name": "output", "required": true, "type": "string"}]}, "edges": null, "id": "069541", "meta": {"position": {"x": 1366.4726325403544, "y": -1137.561587288664}}, "type": "15"}, {"blocks": [], "data": {"inputs": {"concatParams": [{"input": {"type": "string", "value": {"content": "[\n{\"effect_title\":\"金粉闪闪\",\"start\":0,\"end\":{{String3}}}\n]", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "concatResult"}, {"input": {"type": "string", "value": {"content": "，", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "arrayItemConcatChar"}, {"input": {"schema": {"schema": [{"name": "label", "required": true, "type": "string"}, {"name": "value", "required": true, "type": "string"}, {"name": "isDefault", "required": true, "type": "boolean"}], "type": "object"}, "type": "list", "value": {"content": [{"isDefault": true, "label": "换行", "value": "\n"}, {"isDefault": true, "label": "制表符", "value": "\t"}, {"isDefault": true, "label": "句号", "value": "。"}, {"isDefault": true, "label": "逗号", "value": "，"}, {"isDefault": true, "label": "分号", "value": "；"}, {"isDefault": true, "label": "空格", "value": " "}], "type": "literal"}}, "name": "allArrayItemConcatChars"}], "inputParameters": [{"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_s", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String1"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "audio_dur_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String2"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_start_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String3"}, {"input": {"type": "integer", "value": {"content": {"blockID": "156181", "name": "video_end_ns", "source": "block-output"}, "rawMeta": {"type": 2}, "type": "ref"}}, "name": "String4"}, {"input": {"assistType": 8, "type": "string", "value": {"content": {"blockID": "100001", "name": "bg_audio", "source": "block-output"}, "rawMeta": {"type": 14}, "type": "ref"}}, "name": "String6"}, {"input": {"assistType": 8, "type": "string", "value": {"content": {"blockID": "100001", "name": "audio", "source": "block-output"}, "rawMeta": {"type": 14}, "type": "ref"}}, "name": "String8"}], "method": "concat"}, "nodeMeta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-StrConcat-v2.jpg", "mainColor": "#3071F2", "subTitle": "文本处理", "title": "特效"}, "outputs": [{"name": "output", "required": true, "type": "string"}]}, "edges": null, "id": "895301", "meta": {"position": {"x": 1826.4726325403544, "y": -1137.561587288664}}, "type": "15"}, {"blocks": [], "data": {"inputs": {"code": "import re\n\nasync def main(args: Args) -> Output:\n    \"\"\"\n    主函数，处理输入数据并生成指定格式的输出。\n    \n    参数:\n        args (Args): 包含输入参数的对象。\n        \n    返回:\n        dict: 处理后的结果。\n    \"\"\"\n    # 提取输入数据中的 detail 数组\n    input_details = args.params[\"detail\"]\n    \n    # 处理每个句子并构建输出\n    output_list = []\n\n    # 开头的标题\n    # output_list.append({\n    #     \"text\": args.params[\"title\"],\n    #     \"start\": 0,                     # 确保是数字\n    #     \"end\": 1_000_000                          # 确保是数字\n    # })\n    for item in input_details:\n        # 去除句子末尾的标点符号（，。！？）\n        cleaned_text = re.sub(r'[，。！？、\\n]$', '', item[\"sentence\"])\n        \n        # 提取时间范围，并确保 start 和 end 是整数\n        time_range = item[\"time_range\"]\n        start = int(time_range.get(\"start\", 0))  # 强制转换为整数\n        end = int(time_range.get(\"end\", 0))      # 强制转换为整数\n\n        # 构建输出项\n        output_item = {\n            \"text\": cleaned_text,\n            # \"start\": start + 1_000_000,                     # 确保是数字\n            # \"end\": end + 1_000_000                          # 确保是数字\n            \"start\": start,                     # 确保是数字\n            \"end\": end                          # 确保是数字\n        }\n        output_list.append(output_item)\n    \n    return {\"result\": output_list}", "inputParameters": [{"input": {"schema": {"schema": [], "type": "object"}, "type": "list", "value": {"content": {"blockID": "177620", "name": "detail", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "detail"}, {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "title", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "title"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "字幕内容传输字符串转换"}, "outputs": [{"name": "result", "required": false, "type": "string"}]}, "edges": null, "id": "149915", "meta": {"position": {"x": 970.9001816716037, "y": -2592.863693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "# Assuming necessary imports like typing.Dict, typing.Any, typing.List are present\n# Assuming Args and Output types are defined elsewhere\n\nasync def main(args: Args) -> Output:\n    import re\n\n    # 获取输入参数\n    original_text = args.params['text']\n    total_duration = float(args.params['audio_duration'])  # 单位：秒\n\n    # 改进版分割规则：精确匹配中文标点（，。！？；\\n）且保留标点\n    # 在正则表达式中添加分号 '；'\n    sentences = re.split(r'(?<=[，。！？；\\n])\\s*', original_text.strip())  # \\s* 匹配标点后可能的空格\n    sentences = [s for s in sentences if s]  # 移除空字符串\n\n    # 计算有效字符数（不含空格）\n    effective_chars = [len(sentence.replace(\" \", \"\")) for sentence in sentences]\n    total_effective = sum(effective_chars)\n\n    # 计算平均每个有效字符的时长（单位：秒）\n    avg_time_per_char = total_duration / total_effective if total_effective != 0 else 0\n\n    # 分配时间（保留3位小数，单位：秒）\n    time_distribution = [\n        round(count * avg_time_per_char, 3)\n        for count in effective_chars\n    ]\n\n    # --- 新增：合并少于5个有效字符的句子 ---\n    merged_sentences = []\n    merged_effective_chars = []\n    merged_times = []\n\n    for i in range(len(sentences)):\n        current_sentence = sentences[i]\n        current_chars = effective_chars[i]\n        current_time = time_distribution[i]\n\n        # 如果当前句子有效字符少于5个，并且不是第一个句子，则合并到前一个句子\n        if current_chars < 5 and len(merged_sentences) > 0:\n            merged_sentences[-1] += current_sentence\n            merged_effective_chars[-1] += current_chars\n            merged_times[-1] += current_time\n            merged_times[-1] = round(merged_times[-1], 3) # 重新四舍五入避免精度问题\n        else:\n            # 否则，作为一个独立的句子添加\n            merged_sentences.append(current_sentence)\n            merged_effective_chars.append(current_chars)\n            merged_times.append(current_time)\n    # --- 合并逻辑结束 ---\n\n\n    # 构建结果（使用合并后的数据）\n    results = []\n    cumulative_time = 0  # 累积时间（单位：秒）\n    # 使用合并后的列表进行迭代\n    for sentence, chars, time in zip(merged_sentences, merged_effective_chars, merged_times):\n        start_time = int(cumulative_time * 1e6)  # 转换为微秒\n        end_time = int((cumulative_time + time) * 1e6)  # 转换为微秒\n        # 确保结束时间不小于开始时间（处理可能的零时长句子）\n        if end_time <= start_time and time > 0:\n             end_time = start_time + 1 # 至少分配1微秒\n\n        results.append({\n            \"sentence\": sentence,\n            \"time_range\": {\"start\": start_time, \"end\": end_time},\n            \"effective_char_count\": chars,\n            # word_count 可能不再准确反映合并后的情况，但仍保留供参考\n            \"word_count\": len(sentence.split())\n        })\n        cumulative_time += time\n        cumulative_time = round(cumulative_time, 3) # 累积时也注意精度\n\n    return {\"detail\": results}\n", "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "content", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "text"}, {"input": {"type": "float", "value": {"content": {"blockID": "111131", "name": "duration", "source": "block-output"}, "rawMeta": {"type": 4}, "type": "ref"}}, "name": "audio_duration"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "计算文字和对应时间"}, "outputs": [{"name": "detail", "required": false, "schema": {"schema": [], "type": "object"}, "type": "list"}]}, "edges": null, "id": "177620", "meta": {"position": {"x": 2313.8774146365477, "y": -3019.7750876868104}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "async def main(args: Args) -> Output:\n    # 获取输入秒数并转换为浮点数\n    seconds = float(args.params['audio_duration'])\n    \n    # 秒转微秒计算（1秒=1,000,000微秒）\n    microseconds = int(seconds * 1_000_000)  # 使用下划线提高可读性\n    # 全视频时长 = 开头1秒 + 语音长度 + 结尾1秒\n    return {\n        \"audio_dur_s\": float(args.params['audio_duration']), # 音频时长单位：秒\n        \"audio_dur_ns\": microseconds,  # 音频时长单位：微妙\n        # \"video_start_ns\": microseconds + 1_000_000, # 视频开始时间：微秒\n        # \"video_end_ns\": microseconds + 2_000_000, # 视频结束时间：微秒，\n        \"video_start_ns\": microseconds, # 视频开始时间：微秒\n        \"video_end_ns\": microseconds + 1_000_000, # 视频结束时间：微秒，\n    }", "inputParameters": [{"input": {"type": "float", "value": {"content": {"blockID": "111131", "name": "duration", "source": "block-output"}, "rawMeta": {"type": 4}, "type": "ref"}}, "name": "audio_duration"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "音频时长相关参数"}, "outputs": [{"name": "audio_dur_s", "required": false, "type": "integer"}, {"name": "audio_dur_ns", "required": false, "type": "integer"}, {"name": "video_start_ns", "required": false, "type": "integer"}, {"name": "video_end_ns", "required": false, "type": "integer"}]}, "edges": null, "id": "156181", "meta": {"position": {"x": 1853.8774146365477, "y": -3019.7750876868104}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import json\n# ... (假设类型提示已定义或不需要) ...\n\nasync def main(args) -> Output: # Adjust Output type hint as needed\n    params = args.params # params is likely the dictionary: {\"detail\": [...], \"input\": [...]}\n\n    # --- 修改：直接从 params 获取列表 ---\n    # Get the list of English translations from params['input'], default to empty list\n    english_translations_list = params.get('input', [])\n    # Ensure all items are strings\n    translations = [str(item) if item is not None else \"\" for item in english_translations_list]\n\n    # Get the list of detail JSON strings from params['detail'], default to empty list\n    detail_json_strings = params.get('detail', [])\n\n    # --- 不需要修改解析逻辑 ---\n    english_output_list = []\n    num_items = min(len(translations), len(detail_json_strings)) # Use the shorter length\n\n    for i in range(num_items):\n        detail_str = detail_json_strings[i]\n        english_text = translations[i]\n\n        try:\n            # Parse the JSON string from 'detail'\n            detail_data = json.loads(detail_str)\n\n            # Check if parsed data is a dictionary and has 'start' and 'end' keys\n            if isinstance(detail_data, dict) and 'start' in detail_data and 'end' in detail_data:\n                english_output_list.append({\n                    \"end\": detail_data[\"end\"],\n                    \"start\": detail_data[\"start\"],\n                    \"text\": english_text  # Use the English text from the 'input' list\n                })\n            else:\n                # Handle cases where JSON structure is unexpected\n                print(f\"警告：索引 {i} 处的 'detail' 数据格式无效或缺少键: {detail_data}\")\n\n        except json.JSONDecodeError:\n            # Handle cases where a string in 'detail' is not valid JSON\n            print(f\"警告：索引 {i} 处的 'detail' 字符串不是有效的 JSON: {detail_str}\")\n        except Exception as e:\n            # Catch other potential errors\n            print(f\"处理索引 {i} 时发生错误: {e}\")\n\n    # Construct the final result dictionary\n    result = {\"English\": english_output_list}\n\n    # Return the result dictionary directly\n    return result\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "825433", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "461472", "name": "result", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "detail"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "英语字幕"}, "outputs": [{"name": "English", "required": false, "type": "string"}]}, "edges": null, "id": "129955", "meta": {"position": {"x": 2350.900181671602, "y": -2592.863693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import re\n\nasync def main(args: Args) -> Output:\n    \"\"\"\n    主函数，处理输入数据并生成指定格式的输出。\n    \n    参数:\n        args (Args): 包含输入参数的对象。\n        \n    返回:\n        dict: 处理后的结果。\n    \"\"\"\n    # 提取输入数据中的 detail 数组\n    input_details = args.params[\"detail\"]\n    \n    # 处理每个句子并构建输出\n    output_list = []\n\n    # 开头的标题\n    # output_list.append({\n    #     \"text\": args.params[\"title\"],\n    #     \"start\": 0,                     # 确保是数字\n    #     \"end\": 1_000_000                          # 确保是数字\n    # })\n    for item in input_details:\n        # 去除句子末尾的标点符号（，。！？）\n        cleaned_text = re.sub(r'[，。！？、\\n]$', '', item[\"sentence\"])\n        \n        # 提取时间范围，并确保 start 和 end 是整数\n        time_range = item[\"time_range\"]\n        start = int(time_range.get(\"start\", 0))  # 强制转换为整数\n        end = int(time_range.get(\"end\", 0))      # 强制转换为整数\n\n        # 构建输出项\n        output_item = {\n            \"text\": cleaned_text,\n            # \"start\": start + 1_000_000,                     # 确保是数字\n            # \"end\": end + 1_000_000                          # 确保是数字\n            \"start\": start,                     # 确保是数字\n            \"end\": end                          # 确保是数字\n        }\n        output_list.append(output_item)\n    \n    return {\"result\": output_list}", "inputParameters": [{"input": {"schema": {"schema": [], "type": "object"}, "type": "list", "value": {"content": {"blockID": "177620", "name": "detail", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "detail"}, {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "title", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "title"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "英文字幕整理"}, "outputs": [{"name": "result", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "461472", "meta": {"position": {"x": 1890.9001816716018, "y": -2592.863693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import json\nimport logging\n\nlogging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n\n# 假设 Args 和 Output 类已定义\n# class Args:\n#     def __init__(self, params): self.params = params\n# class Output(dict): pass\n\nasync def main(args: Args) -> Output:\n    params = args.params\n    logging.info(f\"接收到的参数: {params}\")\n\n    input_value = params.get('input', None)\n    logging.info(f\"原始 input_value 类型: {type(input_value)}\")\n\n    data = None\n    try:\n        if isinstance(input_value, str):\n            if not input_value.strip():\n                 logging.warning(\"输入字符串为空。\")\n                 data = None\n            else:\n                logging.info(\"输入是字符串，尝试 JSON 解析。\")\n                data = json.loads(input_value)\n        elif isinstance(input_value, (dict, list)):\n             logging.info(\"输入已经是字典或列表。\")\n             data = input_value\n        elif input_value is None:\n             logging.warning(\"输入值为 None。\")\n             data = None\n        else:\n            logging.error(f\"非预期的输入类型: {type(input_value)}。\")\n            data = None\n    except json.JSONDecodeError as e:\n        logging.error(f\"输入的 JSON 格式无效: {e}\")\n        raise ValueError(f\"输入的 'input' 字符串参数 JSON 格式无效: {e}\")\n    except Exception as e:\n        logging.error(f\"处理输入时出错: {e}\")\n        raise ValueError(f\"处理输入时出错: {e}\")\n\n    logging.info(f\"解析后的数据对象类型: {type(data)}\")\n\n    # 提取 time_range 字典列表\n    time_range_dicts = []\n    items_list = None\n\n    if isinstance(data, list):\n        logging.info(\"解析的数据是一个列表，直接处理。\")\n        items_list = data\n    elif isinstance(data, dict):\n        if 'input' in data and isinstance(data.get('input'), list):\n            logging.info(\"在字典中找到 'input' 列表。\")\n            items_list = data['input']\n        elif 'detail' in data and isinstance(data.get('detail'), list):\n            logging.info(\"在字典中找到 'detail' 列表。\")\n            items_list = data['detail']\n        else:\n            logging.warning(\"解析的数据是字典，但未找到 'input' 或 'detail' 列表键。\")\n    else:\n        logging.warning(\"解析的数据不是列表或包含预期键 ('input', 'detail') 的字典，无法提取项目。\")\n\n    if items_list:\n        logging.info(f\"开始从 {len(items_list)} 个项目中提取 time_range 字典。\")\n        for i, item in enumerate(items_list):\n            if isinstance(item, dict) and 'time_range' in item and isinstance(item.get('time_range'), dict):\n                time_range_data = item['time_range']\n                if 'start' in time_range_data and 'end' in time_range_data and \\\n                   isinstance(time_range_data['start'], (int, float)) and \\\n                   isinstance(time_range_data['end'], (int, float)):\n                    time_range_dicts.append(time_range_data)\n                else:\n                    logging.warning(f\"列表中的项目 {i+1} 的 time_range 字典缺少 'start'/'end' 或非数字: {time_range_data}\")\n            else:\n                 logging.warning(f\"列表中的项目 {i+1} 不是字典或缺少 'time_range' 字典: {item}\")\n\n    # --- 两两配对合并时间范围的逻辑 ---\n    pairwise_merged_ranges_str_list = []\n    first_not_merged_ranges_str_list = []\n\n    if not time_range_dicts:\n        logging.info(\"没有提取到 time_range 字典，无需合并。\")\n    else:\n        logging.info(f\"开始两两配对合并 {len(time_range_dicts)} 个提取到的 time_range。\")\n        \n        # 第一组: 第一个不合并，后面的合并\n        first_not_merged_ranges_str_list.append(json.dumps(time_range_dicts[0], ensure_ascii=False))  # 将第一个直接添加\n        for i in range(1, len(time_range_dicts), 2):\n            if i + 1 < len(time_range_dicts):\n                range1 = time_range_dicts[i]\n                range2 = time_range_dicts[i + 1]\n                start_time = range1.get('start')\n                end_time = range2.get('end')\n                combined_range = {\"start\": start_time, \"end\": end_time}\n                first_not_merged_ranges_str_list.append(json.dumps(combined_range, ensure_ascii=False))\n            else:\n                first_not_merged_ranges_str_list.append(json.dumps(time_range_dicts[i], ensure_ascii=False))\n\n        # 第二组: 正常两两合并\n        for i in range(0, len(time_range_dicts), 2):\n            if i + 1 < len(time_range_dicts):\n                range1 = time_range_dicts[i]\n                range2 = time_range_dicts[i + 1]\n                start_time = range1.get('start')\n                end_time = range2.get('end')\n                combined_range = {\"start\": start_time, \"end\": end_time}\n                pairwise_merged_ranges_str_list.append(json.dumps(combined_range, ensure_ascii=False))\n            else:\n                pairwise_merged_ranges_str_list.append(json.dumps(time_range_dicts[i], ensure_ascii=False))\n\n        logging.info(f\"第一组（第一个不合并，后面的合并）生成了 {len(first_not_merged_ranges_str_list)} 个时间范围字符串。\")\n        logging.info(f\"第二组（正常两两合并）生成了 {len(pairwise_merged_ranges_str_list)} 个时间范围字符串。\")\n\n    # 返回两个输出\n    ret: Output = {\n        \"first_not_merged\": first_not_merged_ranges_str_list,\n        \"pairwise_merged\": pairwise_merged_ranges_str_list\n    }\n    logging.info(f\"返回的输出: {ret}\")\n    return ret\n", "inputParameters": [{"input": {"schema": {"schema": [], "type": "object"}, "type": "list", "value": {"content": {"blockID": "177620", "name": "detail", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "图片时间线二合一"}, "outputs": [{"name": "first_not_merged", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "pairwise_merged", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "117382", "meta": {"position": {"x": 510.90018167160366, "y": -2592.863693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"batch": {"batchEnable": true, "batchSize": 100, "concurrentSize": 10, "inputLists": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "547995", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "item1"}]}, "fcParamVar": {"knowledgeFCParam": {}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "139227", "name": "item1", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "llmParam": [{"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "1024", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "integer", "value": {"content": "1742989917", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "string", "value": {"content": "{{input}}输出内容限制不能低于150字不能超过200字的描述", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "# 角色\n你是一个优秀的AI绘画描述词生成家，擅长把输入的句子{{input}}生成一份适用于黑色火柴人的AI绘画的描述词\n\n## 要求\n一个句子只需要生成一份黑色火柴人的描述词，不要同一句子生成多份，自由描述角色的行为动作，不要违反正常的行为，一切的动作都要符合正常的行为，反映句子的情感或心理学主题，此描述词用来绘制启蒙图片，不要出现案例输出文案，输出只针对{{input}}，请严格按照我的要求回答\n\n## 示例\n通用模板：极简主义纯黑白色矢量画，圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：火柴人数量+ 行为动作 +情绪描述+ 情绪符号 +极简场景物品，儿童简笔画风格，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n    要求：增加符号、物品等简约装饰元素增加氛围\n  ### 提示词参考案例：\n    1. 极简主义纯黑白色矢量画，萌版、圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：2个火柴人，激烈争吵，情绪激动，愤怒符号，站在极简街道上 ，儿童简笔画风格，萌版，圆头，无眼睛，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n     2. 极简主义纯黑白色矢量画，萌版、圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：一个火柴人半低着身体，情绪低落 ，头顶乌云，带点小雨，儿童简笔画风格，萌版，圆头，无眼睛，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n    3. 极简主义纯黑白色矢量画，萌版、圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：三个火柴人高兴庆祝，烟花，儿童简笔画风格，萌版，圆头，无眼睛，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n    4. 极简主义纯黑白色矢量画，萌版、圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：一个火柴人打翻牛奶洒在地上，慌张，惊慌符号，儿童简笔画风格，萌版，圆头，无眼睛，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n    5.极简主义纯黑白色矢量画，萌版、圆头、无眼睛黑色火柴人，人物纯黑色实心填充，身材比例适中，粗体块状四肢，四肢圆润充实，结构分明，纯白色背景，高对比度，二维平面设计风格，无阴影无渐变：两个火柴人，女火柴人感到惊讶，男火柴人淡定思考，思考符号，儿童简笔画风格，萌版，圆头，无眼睛，白色嘴巴，超高清晰度矢量图，轮廓清晰无描边。\n## 输出要求\n把内容全部输出，不能有遗漏的内容\n输出内容限制不能低于150字不能超过200字的描述\n请严格按照上述提示进行操作以及输出内容", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "stableSystemPrompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "canContinue"}], "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 600000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "subTitle": "大模型", "title": "图片提示词"}, "outputs": [{"name": "outputList", "required": false, "schema": {"schema": [{"name": "output", "type": "string"}], "type": "object"}, "type": "list"}], "version": "3"}, "edges": null, "id": "139227", "meta": {"position": {"x": 2810.900181671602, "y": -2618.863693198731}}, "type": "3"}, {"blocks": [], "data": {"inputs": {"code": "import json\n# Assuming Args and Output are defined elsewhere, e.g.:\n# from typing import List, Dict, Any\n# class Args:\n#     params: Dict[str, Any]\n# class Output(dict):\n#     pass\n\nasync def main(args: Args) -> Output:\n    params = args.params\n\n    # 获取 'input' 参数，确保它是一个列表\n    input_list = params.get('input', [])\n    if not isinstance(input_list, list):\n        raise ValueError(\"Expected 'input' to be a list of JSON strings\")\n\n    # 初始化列表\n    singular_items = [] # 存放奇数索引项\n    even_number_items = [] # 存放偶数索引项\n\n    # 遍历输入列表，根据索引是奇数还是偶数分配字符串\n    for index, item_str in enumerate(input_list):\n        # 可选：验证 item_str 是否为有效 JSON\n        # try:\n        #     json.loads(item_str)\n        # except json.JSONDecodeError:\n        #     print(f\"Warning: Skipping invalid JSON string at index {index}: {item_str}\")\n        #     continue # 跳过无效项\n\n        if index % 2 != 0:  # 索引是奇数\n            singular_items.append(item_str) # 添加原始字符串\n        else:  # 索引是偶数 (包括 0)\n            even_number_items.append(item_str) # 添加原始字符串\n\n    # 构建最终的输出字典\n    ret: Output = {\n        \"singular\": singular_items,\n        \"even_numbers\": even_number_items\n    }\n    return ret\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "849106", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "时间线分开"}, "outputs": [{"name": "even_numbers", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "singular", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "130348", "meta": {"position": {"x": 2332.6664086454084, "y": -1949.8027201192524}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import json\n# Assuming Args and Output are defined elsewhere, e.g.:\n# from typing import List, Dict, Any\n# class Args:\n#     params: Dict[str, Any]\n# class Output(dict):\n#     pass\n\nasync def main(args: Args) -> Output:\n    params = args.params\n\n    # 获取 'input' 参数，确保它是一个列表\n    input_list = params.get('input', [])\n    if not isinstance(input_list, list):\n        raise ValueError(\"Expected 'input' to be a list of JSON strings\")\n\n    # 初始化列表\n    singular_items = [] # 存放奇数索引项\n    even_number_items = [] # 存放偶数索引项\n\n    # 遍历输入列表，根据索引是奇数还是偶数分配字符串\n    for index, item_str in enumerate(input_list):\n        # 可选：验证 item_str 是否为有效 JSON\n        # try:\n        #     json.loads(item_str)\n        # except json.JSONDecodeError:\n        #     print(f\"Warning: Skipping invalid JSON string at index {index}: {item_str}\")\n        #     continue # 跳过无效项\n\n        if index % 2 != 0:  # 索引是奇数\n            singular_items.append(item_str) # 添加原始字符串\n        else:  # 索引是偶数 (包括 0)\n            even_number_items.append(item_str) # 添加原始字符串\n\n    # 构建最终的输出字典\n    ret: Output = {\n        \"singular\": singular_items,\n        \"even_numbers\": even_number_items\n    }\n    return ret\n", "inputParameters": [{"input": {"schema": {"assistType": 2, "type": "string"}, "type": "list", "value": {"content": {"blockID": "194925", "name": "imagelist", "source": "block-output"}, "rawMeta": {"type": 104}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "图片分开"}, "outputs": [{"name": "singular", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "even_numbers", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "141842", "meta": {"position": {"x": 2332.6664086454084, "y": -1735.7027201192525}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7457837925833883688", "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "add_images", "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7457837925833801768", "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "视频合成_剪映小助手", "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "x缩放", "input": {}, "name": "scale_x", "required": false, "type": "float"}, {"description": "y缩放", "input": {}, "name": "scale_y", "required": false, "type": "float"}, {"description": "移动transform_x", "input": {}, "name": "transform_x", "required": false, "type": "float"}, {"description": "移动transform_y", "input": {}, "name": "transform_y", "required": false, "type": "float"}, {"description": "草稿地址，使用create_draft输出的draft_url即可", "input": {}, "name": "draft_url", "required": true, "type": "string"}, {"description": "查看说明：https://krxc4izye0.feishu.cn/wiki/Pgm9wXA4EipKhYkeEQJcLBlJnWb?from=from_copylink", "input": {}, "name": "image_infos", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "167928", "name": "draft_url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "draft_url"}, {"input": {"type": "string", "value": {"content": {"blockID": "553483", "name": "images", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "image_infos"}, {"input": {"type": "float", "value": {"content": 0.4, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_x"}, {"input": {"type": "float", "value": {"content": 0.4, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "scale_y"}, {"input": {"type": "float", "value": {"content": -793, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "transform_x"}, {"input": {"type": "float", "value": {"content": -210, "rawMeta": {"type": 4}, "type": "literal"}}, "name": "transform_y"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "批量添加图片", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "视频合成_剪映小助手:add_images", "title": "add_images左边图片"}, "outputs": [{"name": "draft_url", "required": false, "type": "string"}, {"name": "image_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_ids", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "segment_infos", "required": false, "schema": {"schema": [{"name": "end", "required": false, "type": "integer"}, {"name": "id", "required": false, "type": "string"}, {"name": "start", "required": false, "type": "integer"}], "type": "object"}, "type": "list"}, {"name": "track_id", "required": false, "type": "string"}]}, "edges": null, "id": "438515", "meta": {"position": {"x": -448.29583789482325, "y": -458.8707193925587}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"code": "import json\nimport logging\n\nlogging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n\n# 假设 Args 和 Output 类已定义\n# class Args:\n#     def __init__(self, params): self.params = params\n# class Output(dict): pass\n\nasync def main(args: Args) -> Output:\n    params = args.params\n    logging.info(f\"接收到的参数: {params}\")\n\n    input_value = params.get('input', None)\n    logging.info(f\"原始 input_value 类型: {type(input_value)}\")\n\n    data = None\n    try:\n        if isinstance(input_value, str):\n            if not input_value.strip():\n                 logging.warning(\"输入字符串为空。\")\n                 data = None\n            else:\n                logging.info(\"输入是字符串，尝试 JSON 解析。\")\n                data = json.loads(input_value)\n        elif isinstance(input_value, (dict, list)):\n             logging.info(\"输入已经是字典或列表。\")\n             data = input_value\n        elif input_value is None:\n             logging.warning(\"输入值为 None。\")\n             data = None\n        else:\n            logging.error(f\"非预期的输入类型: {type(input_value)}。\")\n            data = None\n    except json.JSONDecodeError as e:\n        logging.error(f\"输入的 JSON 格式无效: {e}\")\n        raise ValueError(f\"输入的 'input' 字符串参数 JSON 格式无效: {e}\")\n    except Exception as e:\n        logging.error(f\"处理输入时出错: {e}\")\n        raise ValueError(f\"处理输入时出错: {e}\")\n\n    logging.info(f\"解析后的数据对象类型: {type(data)}\")\n\n    # 提取 time_range 字典列表\n    time_range_dicts = []\n    items_list = None\n\n    if isinstance(data, list):\n        logging.info(\"解析的数据是一个列表，直接处理。\")\n        items_list = data\n    elif isinstance(data, dict):\n        if 'input' in data and isinstance(data.get('input'), list):\n            logging.info(\"在字典中找到 'input' 列表。\")\n            items_list = data['input']\n        elif 'detail' in data and isinstance(data.get('detail'), list):\n            logging.info(\"在字典中找到 'detail' 列表。\")\n            items_list = data['detail']\n        else:\n            logging.warning(\"解析的数据是字典，但未找到 'input' 或 'detail' 列表键。\")\n    else:\n        logging.warning(\"解析的数据不是列表或包含预期键 ('input', 'detail') 的字典，无法提取项目。\")\n\n    if items_list:\n        logging.info(f\"开始从 {len(items_list)} 个项目中提取 time_range 字典。\")\n        for i, item in enumerate(items_list):\n            if isinstance(item, dict) and 'time_range' in item and isinstance(item.get('time_range'), dict):\n                time_range_data = item['time_range']\n                if 'start' in time_range_data and 'end' in time_range_data and \\\n                   isinstance(time_range_data['start'], (int, float)) and \\\n                   isinstance(time_range_data['end'], (int, float)):\n                    time_range_dicts.append(time_range_data)\n                else:\n                    logging.warning(f\"列表中的项目 {i+1} 的 time_range 字典缺少 'start'/'end' 或非数字: {time_range_data}\")\n            else:\n                 logging.warning(f\"列表中的项目 {i+1} 不是字典或缺少 'time_range' 字典: {item}\")\n\n    # --- 两两配对合并时间范围的逻辑 --- (已移除)\n    # 之前的合并逻辑已被移除\n\n    # 直接使用提取到的 time_range 字典列表作为输出\n    ret: Output = {\n        \"output\": time_range_dicts  # 直接返回提取到的 time_range 字典列表\n    }\n    logging.info(f\"返回的输出: {ret}\")\n    return ret\n", "inputParameters": [{"input": {"schema": {"schema": [], "type": "object"}, "type": "list", "value": {"content": {"blockID": "177620", "name": "detail", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "图片时间线（全部内容）"}, "outputs": [{"name": "output", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "849106", "meta": {"position": {"x": 2773.8774146365477, "y": -3019.7750876868104}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "async function main({ params }: { params: { detail: string[], input: string[], even_numbers: string[] } }): Promise<{ images: string }> {\n\n    // --- 记录收到的 params 内容 (可选，用于调试) ---\n    console.log(\"Received params:\", JSON.stringify(params, null, 2));\n\n    // --- 从 params 中获取 detail, input 和 even_numbers ---\n    const detail = params.detail;\n    const input = params.input;\n    const evenNumbers = params.even_numbers || []; // Default to an empty array if not provided\n\n    // --- Perform the validation checks on params.detail, params.input, and params.even_numbers ---\n    let images = [];\n\n    // 校验 params.detail\n    if (!detail || !Array.isArray(detail) || detail.length === 0) {\n        if (!params || typeof params !== 'object') throw new Error(\"Execution Error: Expected 'params' object in argument, but not found.\");\n        if (!params.hasOwnProperty('detail')) throw new Error(\"Execution Error: 'params' object does not have a 'detail' key.\");\n        throw new Error(`Validation Error: 'params.detail' is not a valid non-empty array. Value: ${JSON.stringify(detail)}`);\n    }\n\n    // 校验 params.input\n    if (!input || !Array.isArray(input) || input.length === 0) {\n        if (!params.hasOwnProperty('input')) throw new Error(\"Execution Error: 'params' object does not have an 'input' key.\");\n        throw new Error(`Validation Error: 'params.input' is not a valid non-empty array. Value: ${JSON.stringify(input)}`);\n    }\n\n    // 校验 params.even_numbers\n    if (!evenNumbers || !Array.isArray(evenNumbers) || evenNumbers.length === 0) {\n        if (!params.hasOwnProperty('even_numbers')) throw new Error(\"Execution Error: 'params' object does not have an 'even_numbers' key.\");\n        throw new Error(`Validation Error: 'params.even_numbers' is not a valid non-empty array. Value: ${JSON.stringify(evenNumbers)}`);\n    }\n\n    // 校验长度\n    if (detail.length !== input.length || detail.length !== evenNumbers.length) {\n        throw new Error(`Input Error: The length of params.detail (${detail.length}), params.input (${input.length}), and params.even_numbers (${evenNumbers.length}) arrays must be the same.`);\n    }\n\n    // --- Rest of the logic (parsing time, creating image objects) ---\n    let timeData;\n    try {\n        // 解析 params.input\n        timeData = input.map(timeStr => {\n            if (typeof timeStr !== 'string') {\n                throw new Error(`Input Error: Expected time data item in params.input to be a string, but got: ${typeof timeStr}`);\n            }\n            return JSON.parse(timeStr);\n        });\n    } catch (e: any) {\n        throw new Error(`Parsing Error: Failed to parse time data in 'params.input': ${e.message}`);\n    }\n\n    // 使用 params.detail 和 even_numbers\n    for (let index = 0; index < detail.length; index++) {\n        const imageUrl = detail[index];\n\n        // Skip empty URLs (\"\" entries)\n        if (imageUrl === \"\") {\n            continue; // Skip this iteration if the URL is empty\n        }\n\n        if (typeof imageUrl !== 'string') {\n            throw new Error(`Input Error: Expected image URL item in params.detail to be a string at index ${index}, but got: ${typeof imageUrl}`);\n        }\n        \n        const { start: imageStart, end: imageEnd } = timeData[index];\n        if (typeof imageStart !== 'number' || typeof imageEnd !== 'number') {\n            throw new Error(`Input Error: Invalid start/end time found in parsed timeData at index ${index}: start=${imageStart}, end=${imageEnd}`);\n        }\n\n        const animation = evenNumbers[index]; // Use the corresponding animation from even_numbers\n\n        images.push({\n            \"image_url\": imageUrl,\n            \"width\": 1920, \"height\": 1080,\n            \"start\": imageStart, \"end\": imageEnd,\n            \"in_animation\": animation, // Use the animation from even_numbers\n            \"out_animation\": \"\", \"loop_animation\": \"\",\n            \"in_animation_duration\": 500000, \"out_animation_duration\": 20000\n        });\n    }\n\n    return {\n        images: JSON.stringify(images)\n    };\n}\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "117382", "name": "pairwise_merged", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "141842", "name": "even_numbers", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "detail"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "314185", "name": "even_numbers", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "even_numbers"}], "language": 5, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "左边图片"}, "outputs": [{"name": "images", "required": false, "type": "string"}]}, "edges": null, "id": "553483", "meta": {"position": {"x": -13.527367459645514, "y": -1137.561587288664}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7361316775017103398", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "wennuan_ahu", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7361316775017054246", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "中文文本转语音", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "content", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "text"}], "settingOnError": {"processType": 1, "retryTimes": 0, "timeoutMs": 180000}}, "nodeMeta": {"description": "将输入的文本转化为温柔的男声", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "中文文本转语音:wennuan_ahu", "title": "ai配音"}, "outputs": [{"name": "code", "required": false, "type": "float"}, {"name": "data", "required": false, "schema": [{"name": "url", "required": false, "type": "string"}, {"name": "message", "required": false, "type": "string"}], "type": "object"}, {"name": "log_id", "required": false, "type": "string"}, {"name": "msg", "required": false, "type": "string"}]}, "edges": null, "id": "105836", "meta": {"position": {"x": -357.6829843441328, "y": -3032.675087686811}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"branches": [{"condition": {"conditions": [{"left": {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "audio", "source": "block-output"}, "type": "ref"}}}, "operator": 9}], "logic": 2}}]}, "nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行“否则”分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "音频选择"}}, "edges": null, "id": "104474", "meta": {"position": {"x": -756.1225853634504, "y": -3032.675087686811}}, "type": "8"}, {"blocks": [], "data": {"inputs": {"mergeGroups": [{"name": "Group1", "variables": [{"type": "string", "value": {"content": {"blockID": "105836", "name": "data.url", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, {"type": "string", "value": {"content": {"blockID": "178546", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}]}]}, "nodeMeta": {"description": "对多个分支的输出进行聚合处理", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/VariableMerge-icon.jpg", "mainColor": "#00B2B2", "subTitle": "变量聚合", "title": "音频内容汇总"}, "outputs": [{"name": "Group1", "type": "string"}]}, "edges": null, "id": "174010", "meta": {"position": {"x": 163.87741463654953, "y": -3020.7750876868104}}, "type": "32"}, {"blocks": [], "data": {"inputs": {"concatParams": [{"input": {"type": "string", "value": {"content": "{{String1}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "concatResult"}, {"input": {"type": "string", "value": {"content": "，", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "arrayItemConcatChar"}, {"input": {"schema": {"schema": [{"name": "label", "required": true, "type": "string"}, {"name": "value", "required": true, "type": "string"}, {"name": "isDefault", "required": true, "type": "boolean"}], "type": "object"}, "type": "list", "value": {"content": [{"isDefault": true, "label": "换行", "value": "\n"}, {"isDefault": true, "label": "制表符", "value": "\t"}, {"isDefault": true, "label": "句号", "value": "。"}, {"isDefault": true, "label": "逗号", "value": "，"}, {"isDefault": true, "label": "分号", "value": "；"}, {"isDefault": true, "label": "空格", "value": " "}], "type": "literal"}}, "name": "allArrayItemConcatChars"}], "inputParameters": [{"input": {"assistType": 8, "type": "string", "value": {"content": {"blockID": "100001", "name": "audio", "source": "block-output"}, "rawMeta": {"type": 14}, "type": "ref"}}, "name": "String1"}], "method": "concat"}, "nodeMeta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-StrConcat-v2.jpg", "mainColor": "#3071F2", "subTitle": "文本处理", "title": "配音处理"}, "outputs": [{"name": "output", "required": true, "type": "string"}]}, "edges": null, "id": "178546", "meta": {"position": {"x": -357.6829843441329, "y": -2850.26073603042}}, "type": "15"}, {"blocks": [{"blocks": [], "data": {"inputs": {"fcParamVar": {"knowledgeFCParam": {}}, "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "154520", "name": "input", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "input"}], "llmParam": [{"input": {"type": "float", "value": {"content": "0.8", "rawMeta": {"type": 4}, "type": "literal"}}, "name": "temperature"}, {"input": {"type": "integer", "value": {"content": "1024", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "integer", "value": {"content": "2", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "string", "value": {"content": "DeepSeek-V3-0324", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "modleName"}, {"input": {"type": "integer", "value": {"content": "1742989917", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "balance", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "string", "value": {"content": "{{input}}", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "rawMeta": {"type": 2}, "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "根据图片描述{{input}}的情绪氛围、内容节奏、构图特点和画面动静感，从以下选项中选择最匹配的进场特效，仅返回一个特效名称，不含任何解释：展开、横向模糊、拼图、渐显、动感放大、向上滑动、轻微放大、缩小", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "systemPrompt"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "stableSystemPrompt"}, {"input": {"type": "boolean", "value": {"content": false, "rawMeta": {"type": 3}, "type": "literal"}}, "name": "canContinue"}], "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 600000}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "required": false, "type": "string"}], "version": "3"}, "edges": null, "id": "131340", "meta": {"position": {"x": 180, "y": 0}}, "type": "3"}], "data": {"inputs": {"batchSize": {"type": "integer", "value": {"content": "100", "type": "literal"}}, "concurrentSize": {"type": "integer", "value": {"content": "10", "type": "literal"}}, "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "547995", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}]}, "nodeMeta": {"description": "通过设定批量运行次数和逻辑，运行批处理体内的任务", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Batch-v2.jpg", "mainColor": "#00B2B2", "subTitle": "批处理", "title": "入场动画情绪"}, "outputs": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "131340", "name": "output", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "output"}]}, "edges": [{"sourceNodeID": "154520", "sourcePortID": "batch-function-inline-output", "targetNodeID": "131340"}, {"sourceNodeID": "131340", "sourcePortID": "", "targetNodeID": "154520", "targetPortID": "batch-function-inline-input"}], "id": "154520", "meta": {"canvasPosition": {"x": 822.6664086454103, "y": -1762.7527201192527}, "position": {"x": 1772.666408645412, "y": -2056.852720119253}}, "type": "28"}, {"blocks": [], "data": {"inputs": {"code": "import json\n# Assuming Args and Output are defined elsewhere, e.g.:\n# from typing import List, Dict, Any\n# class Args:\n#     params: Dict[str, Any]\n# class Output(dict):\n#     pass\n\nasync def main(args: Args) -> Output:\n    params = args.params\n\n    # 获取 'input' 参数，确保它是一个列表\n    input_list = params.get('input', [])\n    if not isinstance(input_list, list):\n        raise ValueError(\"Expected 'input' to be a list of JSON strings\")\n\n    # 初始化列表\n    singular_items = [] # 存放奇数索引项\n    even_number_items = [] # 存放偶数索引项\n\n    # 遍历输入列表，根据索引是奇数还是偶数分配字符串\n    for index, item_str in enumerate(input_list):\n        # 可选：验证 item_str 是否为有效 JSON\n        # try:\n        #     json.loads(item_str)\n        # except json.JSONDecodeError:\n        #     print(f\"Warning: Skipping invalid JSON string at index {index}: {item_str}\")\n        #     continue # 跳过无效项\n\n        if index % 2 != 0:  # 索引是奇数\n            singular_items.append(item_str) # 添加原始字符串\n        else:  # 索引是偶数 (包括 0)\n            even_number_items.append(item_str) # 添加原始字符串\n\n    # 构建最终的输出字典\n    ret: Output = {\n        \"singular\": singular_items,\n        \"even_numbers\": even_number_items\n    }\n    return ret\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "154520", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "入场动画分开"}, "outputs": [{"name": "even_numbers", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "singular", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "314185", "meta": {"position": {"x": 2332.6664086454084, "y": -2163.902720119253}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "async function main({ params }: { params: { detail: string[], input: string[], singular: string[] } }): Promise<{ images: string }> { \n\n    // --- 记录收到的 params 内容 (可选，用于调试) ---\n    console.log(\"Received params:\", JSON.stringify(params, null, 2));\n\n    // --- 从 params 中获取 detail, input 和 singular ---\n    const detail = params.detail;\n    const input = params.input;\n    const singular = params.singular || []; // Default to an empty array if not provided\n\n    // --- Perform the validation checks on params.detail, params.input, and params.singular ---\n    let images = [];\n\n    // 校验 params.detail\n    if (!detail || !Array.isArray(detail) || detail.length === 0) {\n        if (!params || typeof params !== 'object') throw new Error(\"Execution Error: Expected 'params' object in argument, but not found.\");\n        if (!params.hasOwnProperty('detail')) throw new Error(\"Execution Error: 'params' object does not have a 'detail' key.\");\n        throw new Error(`Validation Error: 'params.detail' is not a valid non-empty array. Value: ${JSON.stringify(detail)}`);\n    }\n\n    // 校验 params.input\n    if (!input || !Array.isArray(input) || input.length === 0) {\n        if (!params.hasOwnProperty('input')) throw new Error(\"Execution Error: 'params' object does not have an 'input' key.\");\n        throw new Error(`Validation Error: 'params.input' is not a valid non-empty array. Value: ${JSON.stringify(input)}`);\n    }\n\n    // 校验 params.singular\n    if (!singular || !Array.isArray(singular) || singular.length === 0) {\n        if (!params.hasOwnProperty('singular')) throw new Error(\"Execution Error: 'params' object does not have a 'singular' key.\");\n        throw new Error(`Validation Error: 'params.singular' is not a valid non-empty array. Value: ${JSON.stringify(singular)}`);\n    }\n\n    // 校验长度\n    if (detail.length !== input.length || detail.length !== singular.length) {\n        throw new Error(`Input Error: The length of params.detail (${detail.length}), params.input (${input.length}), and params.singular (${singular.length}) arrays must be the same.`);\n    }\n\n    // --- Rest of the logic (parsing time, creating image objects) ---\n    let timeData;\n    try {\n        // 解析 params.input\n        timeData = input.map(timeStr => {\n            if (typeof timeStr !== 'string') {\n                throw new Error(`Input Error: Expected time data item in params.input to be a string, but got: ${typeof timeStr}`);\n            }\n            return JSON.parse(timeStr);\n        });\n    } catch (e: any) {\n        throw new Error(`Parsing Error: Failed to parse time data in 'params.input': ${e.message}`);\n    }\n\n    // 使用 params.detail 和 singular\n    for (let index = 0; index < detail.length; index++) {\n        const imageUrl = detail[index];\n        if (typeof imageUrl !== 'string') {\n            throw new Error(`Input Error: Expected image URL item in params.detail to be a string at index ${index}, but got: ${typeof imageUrl}`);\n        }\n        const { start: imageStart, end: imageEnd } = timeData[index];\n        if (typeof imageStart !== 'number' || typeof imageEnd !== 'number') {\n            throw new Error(`Input Error: Invalid start/end time found in parsed timeData at index ${index}: start=${imageStart}, end=${imageEnd}`);\n        }\n\n        const animation = singular[index]; // Use the corresponding animation from singular\n\n        images.push({\n            \"image_url\": imageUrl,\n            \"width\": 1920, \"height\": 1080,\n            \"start\": imageStart, \"end\": imageEnd,\n            \"in_animation\": animation, // Use the animation from singular\n            \"out_animation\": \"\", \"loop_animation\": \"\",\n            \"in_animation_duration\": 500000, \"out_animation_duration\": 20000\n        });\n    }\n\n    // Convert images array to the required output format\n    const imagesJson = JSON.stringify(images, null, 2);\n\n    return {\n        images: imagesJson\n    };\n}\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "117382", "name": "first_not_merged", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "196018", "name": "images1", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "detail"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "196018", "name": "images2", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "singular"}], "language": 5, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "右边图片"}, "outputs": [{"name": "images", "required": false, "type": "string"}]}, "edges": null, "id": "090296", "meta": {"position": {"x": -473.5273674596455, "y": -1137.561587288664}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "async def main(args: Args) -> Output:\n    params = args['params']\n    images1 = params['images1']\n    images2 = params['images2']\n\n    # 定义填充的固定内容\n    fixed_detail = \"https://p26-bot-workflow-sign.byteimg.com/tos-cn-i-mdko3gqilj/8e34254f7660405ca83d4c360ca06c2e.png~tplv-mdko3gqilj-image.png?rk3s=c8fe7ad5&x-expires=1775556721&x-signature=mw8sXgl9F3Sn%2FVTXigd8lLkohmU%3D\"\n    fixed_singular = \"渐显\"\n    images1.insert(0, fixed_detail)\n    images2.insert(0, fixed_singular)\n\n    # 构建输出对象\n    ret: Output = {\n        \"images1\": images1,\n        \"images2\": images2,\n    }\n    return ret\n\n    ", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "141842", "name": "singular", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "images1"}, {"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "314185", "name": "singular", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "images2"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "填充合并"}, "outputs": [{"name": "images1", "required": false, "schema": {"type": "string"}, "type": "list"}, {"name": "images2", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "196018", "meta": {"position": {"x": 2792.6664086454084, "y": -1949.8027201192524}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改封面尺寸\",\"type\":\"text\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "156945", "meta": {"position": {"x": 1516.4726325403508, "y": -916.4615872886641}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改字体大小\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"alignment：字体居中调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"font_size：字体大小调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"text_color：字体颜色调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"transform_y：字体位置调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"font：字体设置（需要查看链接才可以替换对应的字体）字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "210750", "meta": {"position": {"x": 871.0036046451233, "y": -302.7243986193005}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改字体大小\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"alignment：字体居中调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"font_size：字体大小调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"text_color：字体颜色调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"transform_y：字体位置调整\"}]},{\"type\":\"paragraph\",\"children\":[{\"type\":\"text\",\"text\":\"font：字体设置（需要查看链接才可以替换对应的字体）字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "827047", "meta": {"position": {"x": 411.0036046451233, "y": -302.7243986193005}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改特效（修改中文的内容）\",\"type\":\"text\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "729389", "meta": {"position": {"x": 1056.4726325403526, "y": -928.4615872886641}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改背景图，一定要和草稿尺寸一起修改到一样的尺寸才可以\",\"type\":\"text\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "687078", "meta": {"position": {"x": -323.52736745964734, "y": -928.4615872886641}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"修改图片提示词\",\"type\":\"text\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "153317", "meta": {"position": {"x": 2040.9001816716018, "y": -2357.763693198731}}, "type": "31"}, {"blocks": [], "data": {"inputs": {"code": "async def main(args: Args) -> Output:\n    # 直接提取input列表中的sentence字段\n    input_list = args.params.get('input', [])\n    \n    # 安全提取所有有效sentence\n    sentences = [\n        item['sentence'] \n        for item in input_list \n        if isinstance(item, dict) and 'sentence' in item\n    ]\n    \n    return {\"output\": sentences}\n", "inputParameters": [{"input": {"schema": {"schema": [], "type": "object"}, "type": "list", "value": {"content": {"blockID": "177620", "name": "detail", "source": "block-output"}, "rawMeta": {"type": 103}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "提取内容"}, "outputs": [{"name": "output", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "547995", "meta": {"position": {"x": -409.09981832839634, "y": -2592.863693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import json\nfrom typing import Any, Dict, List, TypedDict\nimport logging\n\n# Setup basic logging\nlogging.basicConfig(level=logging.INFO)\n\n# --- Type Hint Definitions ---\nclass ResultStructure(TypedDict):\n    content: List[str] # Output key remains 'content'\n\nOutput = ResultStructure\n# --- End Type Hint Definitions ---\n\nasync def main(args: Any) -> Output:\n    \"\"\"\n    Processes an input list of sentences under the 'input' key.\n    Combines the sentences pairwise and returns them under the 'content' key.\n\n    Args:\n        args: An object with a 'params' dict containing 'input' (a list of strings).\n              Example params:\n              {\n                \"input\": [\n                  \"Sentence 1\",\n                  \"Sentence 2\",\n                  \"Sentence 3\"\n                ]\n              }\n\n    Returns:\n        A dictionary with a \"content\" key containing a list of combined strings.\n        Example Output for the above input:\n        {\"content\": [\"Sentence 1 Sentence 2\", \"Sentence 3\"]}\n    \"\"\"\n    logging.info(f\"Function 'main' received args: {args}\")\n    try:\n        logging.info(f\"Accessing args.params: {getattr(args, 'params', 'args has no params attribute')}\")\n    except Exception as e:\n        logging.error(f\"Error accessing args.params: {e}\")\n\n    if not hasattr(args, 'params') or not isinstance(args.params, dict):\n        logging.error(\"Input 'args' must have a 'params' dictionary attribute.\")\n        raise ValueError(\"Input 'args' must have a 'params' dictionary attribute.\")\n\n    params = args.params\n    logging.info(f\"Params content: {params}\")\n\n    # --- 修改：从 'input' 键获取列表 ---\n    # Get the list of sentences directly from params['input']\n    input_sentences = params.get('input', [])\n    logging.info(f\"Extracted 'input' list (type: {type(input_sentences)}): {input_sentences}\")\n\n    # --- 输入验证 ---\n    if not isinstance(input_sentences, list):\n        logging.warning(f\"'input' field is not a list, it's {type(input_sentences)}.\")\n        # Return empty list under the expected key 'content'\n        return {\"content\": []}\n\n    # --- 清理句子 ---\n    cleaned_sentences = [\n        str(s).strip()\n        for s in input_sentences if str(s).strip()\n    ]\n    logging.info(f\"Cleaned sentences count: {len(cleaned_sentences)}\")\n\n    # --- Pairwise Combination (逻辑不变) ---\n    combined_content: List[str] = []\n    num_sentences = len(cleaned_sentences)\n\n    for i in range(0, num_sentences, 2):\n        if i + 1 < num_sentences:\n            combined = f\"{cleaned_sentences[i]} {cleaned_sentences[i+1]}\"\n            combined_content.append(combined)\n        else:\n            combined_content.append(cleaned_sentences[i])\n\n    logging.info(f\"Combined content count: {len(combined_content)}\")\n\n    # Construct the final result dictionary with the 'content' key\n    result: ResultStructure = {\"content\": combined_content}\n    return result\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "547995", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "内容合并2合1_1"}, "outputs": [{"name": "content", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "047111", "meta": {"position": {"x": 50.90018167160366, "y": -2699.9136931987305}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"code": "import json\nfrom typing import Any, Dict, List, TypedDict\nimport logging\n\n# Setup basic logging\nlogging.basicConfig(level=logging.INFO)\n\n# --- Type Hint Definitions ---\nclass ResultStructure(TypedDict):\n    content: List[str] # Output key remains 'content'\n\nOutput = ResultStructure\n# --- End Type Hint Definitions ---\n\nasync def main(args: Any) -> Output:\n    \"\"\"\n    Processes an input list of sentences under the 'input' key.\n    Combines the sentences pairwise and returns them under the 'content' key.\n\n    Args:\n        args: An object with a 'params' dict containing 'input' (a list of strings).\n              Example params:\n              {\n                \"input\": [\n                  \"Sentence 1\",\n                  \"Sentence 2\",\n                  \"Sentence 3\"\n                ]\n              }\n\n    Returns:\n        A dictionary with a \"content\" key containing a list of combined strings.\n        Example Output for the above input:\n        {\"content\": [\"Sentence 1 Sentence 2\", \"Sentence 3\"]}\n    \"\"\"\n    logging.info(f\"Function 'main' received args: {args}\")\n    try:\n        logging.info(f\"Accessing args.params: {getattr(args, 'params', 'args has no params attribute')}\")\n    except Exception as e:\n        logging.error(f\"Error accessing args.params: {e}\")\n\n    if not hasattr(args, 'params') or not isinstance(args.params, dict):\n        logging.error(\"Input 'args' must have a 'params' dictionary attribute.\")\n        raise ValueError(\"Input 'args' must have a 'params' dictionary attribute.\")\n\n    params = args.params\n    logging.info(f\"Params content: {params}\")\n\n    # --- 修改：从 'input' 键获取列表 ---\n    # Get the list of sentences directly from params['input']\n    input_sentences = params.get('input', [])\n    logging.info(f\"Extracted 'input' list (type: {type(input_sentences)}): {input_sentences}\")\n\n    # --- 输入验证 ---\n    if not isinstance(input_sentences, list):\n        logging.warning(f\"'input' field is not a list, it's {type(input_sentences)}.\")\n        # Return empty list under the expected key 'content'\n        return {\"content\": []}\n\n    # --- 清理句子 ---\n    cleaned_sentences = [\n        str(s).strip()\n        for s in input_sentences if str(s).strip()\n    ]\n    logging.info(f\"Cleaned sentences count: {len(cleaned_sentences)}\")\n\n    # --- Pairwise Combination (逻辑不变) ---\n    combined_content: List[str] = []\n    num_sentences = len(cleaned_sentences)\n\n    for i in range(0, num_sentences, 2):\n        if i + 1 < num_sentences:\n            combined = f\"{cleaned_sentences[i]} {cleaned_sentences[i+1]}\"\n            combined_content.append(combined)\n        else:\n            combined_content.append(cleaned_sentences[i])\n\n    logging.info(f\"Combined content count: {len(combined_content)}\")\n\n    # Construct the final result dictionary with the 'content' key\n    result: ResultStructure = {\"content\": combined_content}\n    return result\n", "inputParameters": [{"input": {"schema": {"type": "string"}, "type": "list", "value": {"content": {"blockID": "547995", "name": "output", "source": "block-output"}, "rawMeta": {"type": 99}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "内容合并2合1_2"}, "outputs": [{"name": "content", "required": false, "schema": {"type": "string"}, "type": "list"}]}, "edges": null, "id": "473153", "meta": {"position": {"x": 50.90018167160366, "y": -2485.813693198731}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"apiParam": [{"input": {"type": "string", "value": {"content": "7417655779794157603", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiID"}, {"input": {"type": "string", "value": {"content": "get_audio_duration", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "apiName"}, {"input": {"type": "string", "value": {"content": "7417655779794141219", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginID"}, {"input": {"type": "string", "value": {"content": "获取音频时长", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginName"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "pluginVersion"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "tips"}, {"input": {"type": "string", "value": {"content": "", "rawMeta": {"type": 1}, "type": "literal"}}, "name": "outDocLink"}], "inputDefs": [{"description": "mp3_url", "input": {}, "name": "mp3_url", "required": true, "type": "string"}], "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "174010", "name": "Group1", "source": "block-output"}, "rawMeta": {"type": 1}, "type": "ref"}}, "name": "mp3_url"}], "settingOnError": {"dataOnErr": "{\n    \"duration\": 0,\n    \"message\": \"\"\n}", "processType": 2, "retryTimes": 0, "switch": true, "timeoutMs": 180000}}, "nodeMeta": {"description": "输入音频链接，获取音频时长", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Plugin-v2.jpg", "subtitle": "获取音频时长:get_audio_duration", "title": "音频时长获取"}, "outputs": [{"name": "duration", "required": false, "type": "float"}, {"name": "message", "required": false, "type": "string"}, {"name": "errorBody", "readonly": true, "schema": [{"name": "errorMessage", "readonly": true, "type": "string"}, {"name": "errorCode", "readonly": true, "type": "string"}], "type": "object"}, {"name": "isSuccess", "readonly": true, "type": "boolean"}]}, "edges": null, "id": "111131", "meta": {"position": {"x": 623.8774146365496, "y": -3019.7750876868104}}, "type": "4"}, {"blocks": [], "data": {"inputs": {"code": "# 在这里，您可以通过 ‘args’  获取节点中的输入变量，并通过 'ret' 输出结果\n# 'args' 和 'ret' 已经被正确地注入到环境中\n# 下面是一个示例，首先获取节点的全部输入参数params，其次获取其中参数名为‘input’的值：\n# params = args.params; \n# input = params.input;\n# 下面是一个示例，输出一个包含多种数据类型的 'ret' 对象：\n# ret: Output =  { \"name\": ‘小明’, \"hobbies\": [“看书”, “旅游”] };\n\nasync def main(args: Args) -> Output:\n    params = args.params\n    \n    # 打印传入的参数，帮助调试\n    print(\"Received params:\", params)\n\n    # 检查是否存在 'input' 键\n    if 'input' not in params:\n        return {\n            \"$error\": \"'input' key is missing in the input parameters.\"\n        }\n\n    input_value = params['input']\n\n    # 将负数转为正数\n    output_value = abs(input_value)\n\n    # 打印处理后的结果，帮助调试\n    print(\"Processed value:\", output_value)\n\n    # 构建输出对象\n    ret: Output = {\n        \"output\": output_value,\n    }\n\n    return ret\n", "inputParameters": [{"input": {"type": "float", "value": {"content": {"blockID": "111131", "name": "duration", "source": "block-output"}, "rawMeta": {"type": 4}, "type": "ref"}}, "name": "input"}], "language": 3, "settingOnError": {"processType": 1, "retryTimes": 0, "switch": false, "timeoutMs": 60000}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "代码"}, "outputs": [{"name": "print", "required": false, "type": "string"}]}, "edges": null, "id": "177027", "meta": {"position": {"x": 1443.8774146365477, "y": -3019.7750876868104}}, "type": "5"}, {"blocks": [], "data": {"inputs": {"note": "[{\"type\":\"paragraph\",\"children\":[{\"text\":\"title:标题\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"text\":\"content：自定义文案\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"text\":\"andio：音频文件\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"text\":\"bg_audio：背景音乐\",\"type\":\"text\"}]},{\"type\":\"paragraph\",\"children\":[{\"text\":\"logo：左上角logo图片\",\"type\":\"text\"}]}]", "schemaType": "slate"}, "size": {"height": 150, "width": 240}}, "edges": null, "id": "177206", "meta": {"position": {"x": -1740.195462745428, "y": -2056.8527201192524}}, "type": "31"}], "versions": {"batch": "v2", "loop": "v2"}}