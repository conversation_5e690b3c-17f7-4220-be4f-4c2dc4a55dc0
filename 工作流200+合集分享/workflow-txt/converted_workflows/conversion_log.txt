[2025-08-08 12:05:54] [INFO] 开始批量转换工作流文件...
[2025-08-08 12:06:58] [INFO] 开始处理: Workflow-DZ052_huochairenxinli2_1-draft-7052.zip
[2025-08-08 12:06:58] [INFO] 处理ZIP文件失败 Workflow-DZ052_huochairenxinli2_1-draft-7052.zip: File is not a zip file
[2025-08-08 12:06:58] [INFO] 开始处理: workflow_summary.json
[2025-08-08 12:06:58] [SUCCESS] 成功使用 utf-8 编码提取JSON数据
[2025-08-08 12:06:58] [WARNING] 缺少必需字段: nodes
[2025-08-08 12:06:58] [WARNING] 工作流数据结构无效: workflow_summary.json
[2025-08-08 12:06:58] [SUCCESS] TXT文件已生成: workflow_summary_workflow.txt
[2025-08-08 12:06:58] [INFO] 开始处理: extracted_workflow_fixed.json
[2025-08-08 12:06:58] [SUCCESS] 成功使用 utf-8 编码提取JSON数据
[2025-08-08 12:06:58] [SUCCESS] TXT文件已生成: extracted_workflow_fixed_workflow.txt
[2025-08-08 12:06:58] [INFO] 开始处理: environment_check_report.json
[2025-08-08 12:06:58] [SUCCESS] 成功使用 utf-8 编码提取JSON数据
[2025-08-08 12:06:58] [WARNING] 缺少必需字段: nodes
[2025-08-08 12:06:58] [WARNING] 工作流数据结构无效: environment_check_report.json
[2025-08-08 12:06:58] [SUCCESS] TXT文件已生成: environment_check_report_workflow.txt
[2025-08-08 12:06:58] [SUCCESS] 处理完成！成功: 3, 失败: 1
[2025-08-08 12:06:58] [INFO] 详细报告已保存到: converted_workflows/conversion_report.json
