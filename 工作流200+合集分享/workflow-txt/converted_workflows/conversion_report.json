{"处理信息": {"开始时间": "2025-08-08 12:05:54", "结束时间": "2025-08-08 12:06:58", "处理时长": "63.85 秒", "输入目录": "/Users/<USER>/Desktop/200+工作流/工作流200+合集分享/workflow-txt", "输出目录": "/Users/<USER>/Desktop/200+工作流/工作流200+合集分享/workflow-txt/converted_workflows", "输出格式": "txt"}, "处理统计": {"成功": 3, "失败": 1, "总计": 4, "成功率": "75.0%"}, "处理详情": [{"original": "workflow_summary.json", "base_name": "workflow_summary", "size": 704, "timestamp": "2025-08-08T12:06:58.041441"}, {"original": "extracted_workflow_fixed.json", "base_name": "extracted_workflow_fixed", "size": 238172, "timestamp": "2025-08-08T12:06:58.044714"}, {"original": "environment_check_report.json", "base_name": "environment_check_report", "size": 440, "timestamp": "2025-08-08T12:06:58.045485"}], "输出文件": {"JSON文件": [], "TXT文件": [{"文件名": "workflow_summary_workflow.txt", "大小": "0.4 KB", "路径": "txt/workflow_summary_workflow.txt"}, {"文件名": "environment_check_report_workflow.txt", "大小": "0.4 KB", "路径": "txt/environment_check_report_workflow.txt"}, {"文件名": "extracted_workflow_fixed_workflow.txt", "大小": "12.9 KB", "路径": "txt/extracted_workflow_fixed_workflow.txt"}], "摘要文件": []}}