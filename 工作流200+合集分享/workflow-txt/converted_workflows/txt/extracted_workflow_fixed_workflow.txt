================================================================================
工作流配置文件
================================================================================
生成时间: 2025-08-08 12:06:58

📋 版本信息:
----------------------------------------
  batch: v2
  loop: v2

🔗 节点信息 (共 48 个节点):
----------------------------------------
节点 1:
  ID: 100001
  类型: 1
  位置: (N/A, N/A)
  配置:
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)
    trigger_parameters: list (详见JSON文件)

节点 2:
  ID: 900001
  类型: 2
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)

节点 3:
  ID: 133604
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 4:
  ID: 199678
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 5:
  ID: 120765
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 6:
  ID: 163434
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 7:
  ID: 196263
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 8:
  ID: 157066
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 9:
  ID: 194925
  类型: 28
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 10:
  ID: 167928
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 11:
  ID: 180549
  类型: 23
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 12:
  ID: 176835
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 13:
  ID: 141368
  类型: 15
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 14:
  ID: 825433
  类型: 3
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)
    version: 3

节点 15:
  ID: 069541
  类型: 15
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 16:
  ID: 895301
  类型: 15
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 17:
  ID: 149915
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 18:
  ID: 177620
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 19:
  ID: 156181
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 20:
  ID: 129955
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 21:
  ID: 461472
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 22:
  ID: 117382
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 23:
  ID: 139227
  类型: 3
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)
    version: 3

节点 24:
  ID: 130348
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 25:
  ID: 141842
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 26:
  ID: 438515
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 27:
  ID: 849106
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 28:
  ID: 553483
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 29:
  ID: 105836
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 30:
  ID: 104474
  类型: 8
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)

节点 31:
  ID: 174010
  类型: 32
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 32:
  ID: 178546
  类型: 15
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 33:
  ID: 154520
  类型: 28
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 34:
  ID: 314185
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 35:
  ID: 090296
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 36:
  ID: 196018
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 37:
  ID: 156945
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 38:
  ID: 210750
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 39:
  ID: 827047
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 40:
  ID: 729389
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 41:
  ID: 687078
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 42:
  ID: 153317
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

节点 43:
  ID: 547995
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 44:
  ID: 047111
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 45:
  ID: 473153
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 46:
  ID: 111131
  类型: 4
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 47:
  ID: 177027
  类型: 5
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    nodeMeta: dict (详见JSON文件)
    outputs: list (详见JSON文件)

节点 48:
  ID: 177206
  类型: 31
  位置: (N/A, N/A)
  配置:
    inputs: dict (详见JSON文件)
    size: dict (详见JSON文件)

🔀 连接信息 (共 44 个连接):
----------------------------------------
连接 1:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 2:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 3:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 4:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 5:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 6:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 7:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 8:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 9:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 10:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 11:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 12:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 13:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 14:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 15:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 16:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 17:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 18:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 19:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 20:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 21:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 22:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 23:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 24:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 25:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 26:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 27:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 28:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 29:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 30:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 31:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 32:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 33:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 34:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 35:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 36:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 37:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 38:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 39:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 40:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 41:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 42:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 43:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

连接 44:
  ID: N/A
  源节点: N/A
  目标节点: N/A
  源端口: N/A
  目标端口: N/A

================================================================================
注意: 完整的技术细节请查看对应的JSON文件
================================================================================