#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速TXT格式工作流转换器
专门用于快速将工作流ZIP文件转换为可读的TXT格式
使用方法：将脚本放在包含ZIP文件的目录中，直接运行即可
"""

import os
import json
import zipfile
import shutil
import re
import sys
from pathlib import Path
from datetime import datetime

def extract_json_from_file(file_path):
    """从文件中提取JSON数据"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
    
    for encoding in encodings:
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            content_str = content.decode(encoding, errors='ignore')
            json_start = content_str.find('{')
            
            if json_start == -1:
                continue
            
            json_content = content_str[json_start:]
            
            try:
                data = json.loads(json_content)
                return data
            except json.JSONDecodeError:
                # 尝试修复JSON
                fixed_json = fix_json_content(json_content)
                if fixed_json:
                    try:
                        return json.loads(fixed_json)
                    except json.JSONDecodeError:
                        continue
        except Exception:
            continue
    
    return None

def fix_json_content(json_content):
    """尝试修复JSON内容"""
    try:
        # 移除控制字符
        json_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_content)
        
        # 找到完整的JSON对象
        brace_count = 0
        end_pos = 0
        
        for i, char in enumerate(json_content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_pos = i + 1
                    break
        
        if end_pos > 0:
            candidate = json_content[:end_pos]
            # 修复常见问题
            candidate = re.sub(r',(\s*[}\]])', r'\1', candidate)
            return candidate
        
        return None
    except Exception:
        return None

def format_workflow_as_txt(data, original_filename):
    """将工作流数据格式化为可读的TXT格式"""
    lines = []
    lines.append("=" * 80)
    lines.append("工作流配置文件")
    lines.append("=" * 80)
    lines.append(f"原始文件: {original_filename}")
    lines.append(f"转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    lines.append("")
    
    # 版本信息
    if 'versions' in data and data['versions']:
        lines.append("📋 版本信息:")
        lines.append("-" * 40)
        for key, value in data['versions'].items():
            lines.append(f"  {key}: {value}")
        lines.append("")
    
    # 节点信息
    if 'nodes' in data:
        nodes = data['nodes']
        lines.append(f"🔗 节点信息 (共 {len(nodes)} 个节点):")
        lines.append("-" * 40)
        
        for i, node in enumerate(nodes, 1):
            lines.append(f"节点 {i}:")
            lines.append(f"  ID: {node.get('id', 'N/A')}")
            lines.append(f"  类型: {node.get('type', 'N/A')}")
            
            # 位置信息
            position = node.get('position', {})
            if position:
                x = position.get('x', 'N/A')
                y = position.get('y', 'N/A')
                lines.append(f"  位置: ({x}, {y})")
            
            # 节点数据
            if 'data' in node and node['data']:
                lines.append("  配置:")
                data_items = node['data']
                for key, value in data_items.items():
                    if isinstance(value, (dict, list)):
                        lines.append(f"    {key}: {type(value).__name__} (详见JSON文件)")
                    elif isinstance(value, str) and len(str(value)) > 50:
                        lines.append(f"    {key}: {str(value)[:50]}...")
                    else:
                        lines.append(f"    {key}: {value}")
            lines.append("")
    
    # 连接信息
    if 'edges' in data:
        edges = data['edges']
        lines.append(f"🔀 连接信息 (共 {len(edges)} 个连接):")
        lines.append("-" * 40)
        
        for i, edge in enumerate(edges, 1):
            lines.append(f"连接 {i}:")
            lines.append(f"  ID: {edge.get('id', 'N/A')}")
            lines.append(f"  源节点: {edge.get('source', 'N/A')}")
            lines.append(f"  目标节点: {edge.get('target', 'N/A')}")
            
            source_handle = edge.get('sourceHandle', 'N/A')
            target_handle = edge.get('targetHandle', 'N/A')
            lines.append(f"  连接端口: {source_handle} → {target_handle}")
            lines.append("")
    
    # 统计信息
    lines.append("📊 统计信息:")
    lines.append("-" * 40)
    lines.append(f"  节点总数: {len(data.get('nodes', []))}")
    lines.append(f"  连接总数: {len(data.get('edges', []))}")
    
    # 计算节点类型分布
    if 'nodes' in data:
        node_types = {}
        for node in data['nodes']:
            node_type = node.get('type', 'unknown')
            node_types[node_type] = node_types.get(node_type, 0) + 1
        
        if node_types:
            lines.append("  节点类型分布:")
            for node_type, count in sorted(node_types.items()):
                lines.append(f"    {node_type}: {count}")
    
    lines.append("")
    lines.append("=" * 80)
    lines.append("注意: 这是工作流的简化文本表示")
    lines.append("完整的技术细节请查看对应的JSON文件")
    lines.append("=" * 80)
    
    return '\n'.join(lines)

def process_zip_file(zip_path, output_dir):
    """处理ZIP文件"""
    try:
        # 创建临时目录
        temp_dir = output_dir / f"temp_{zip_path.stem}"
        temp_dir.mkdir(exist_ok=True)
        
        # 解压ZIP文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 查找MATLAB文件
        matlab_files = list(temp_dir.glob("*.zip"))
        if not matlab_files:
            matlab_files = list(temp_dir.glob("*.mat"))
        
        if not matlab_files:
            print(f"  ❌ 在ZIP中未找到MATLAB文件: {zip_path.name}")
            shutil.rmtree(temp_dir)
            return False
        
        # 处理找到的文件
        success = False
        for matlab_file in matlab_files:
            data = extract_json_from_file(matlab_file)
            if data:
                # 生成TXT文件
                txt_content = format_workflow_as_txt(data, zip_path.name)
                txt_file = output_dir / f"{zip_path.stem}_workflow.txt"
                
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(txt_content)
                
                print(f"  ✅ 成功转换: {txt_file.name}")
                success = True
                break
        
        # 清理临时目录
        shutil.rmtree(temp_dir)
        return success
        
    except Exception as e:
        print(f"  ❌ 处理ZIP文件失败 {zip_path.name}: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速TXT格式工作流转换器")
    print("=" * 50)
    
    # 设置目录
    current_dir = Path(".")
    output_dir = Path("txt_workflows")
    output_dir.mkdir(exist_ok=True)
    
    print(f"输入目录: {current_dir.absolute()}")
    print(f"输出目录: {output_dir.absolute()}")
    print()
    
    # 查找ZIP文件
    zip_files = list(current_dir.glob("*.zip"))
    
    if not zip_files:
        print("❌ 未找到任何ZIP文件")
        print("请将工作流ZIP文件放在当前目录下")
        return
    
    print(f"找到 {len(zip_files)} 个ZIP文件:")
    for zip_file in zip_files:
        size_mb = zip_file.stat().st_size / 1024 / 1024
        print(f"  - {zip_file.name} ({size_mb:.1f}MB)")
    print()
    
    # 处理文件
    success_count = 0
    failed_count = 0
    
    print("开始转换...")
    for i, zip_file in enumerate(zip_files, 1):
        print(f"[{i}/{len(zip_files)}] 处理: {zip_file.name}")
        
        if process_zip_file(zip_file, output_dir):
            success_count += 1
        else:
            failed_count += 1
    
    # 显示结果
    print()
    print("=" * 50)
    print("🎉 转换完成！")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {failed_count}")
    print(f"📁 输出目录: {output_dir.absolute()}")
    
    # 列出生成的文件
    txt_files = list(output_dir.glob("*_workflow.txt"))
    if txt_files:
        print(f"\n📝 生成的TXT文件 ({len(txt_files)} 个):")
        for txt_file in txt_files:
            size_kb = txt_file.stat().st_size / 1024
            print(f"  - {txt_file.name} ({size_kb:.1f}KB)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)
