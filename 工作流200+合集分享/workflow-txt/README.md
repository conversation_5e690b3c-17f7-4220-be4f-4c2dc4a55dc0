# 工作流批量转换器使用说明

## 📋 功能概述

这个工具可以将多个工作流ZIP文件批量转换为可读的JSON格式，方便您查看和分析工作流配置。

## 🚀 快速开始

### 方法一：使用简化版脚本（推荐）

1. **准备文件**
   - 将所有工作流ZIP文件放在同一目录下
   - 将 `convert_workflows.py` 脚本也放在同一目录

2. **运行脚本**
   ```bash
   python3 convert_workflows.py
   ```

3. **查看结果**
   - 转换后的JSON文件会保存在 `converted_workflows` 目录中
   - 每个ZIP文件会生成两个文件：
     - `{原文件名}_workflow.json` - 主要数据文件
     - `{原文件名}_summary.json` - 摘要信息文件

### 方法二：使用完整版脚本

1. **基本用法**
   ```bash
   python3 batch_workflow_converter.py
   ```

2. **指定输入输出目录**
   ```bash
   python3 batch_workflow_converter.py --input /path/to/input --output /path/to/output
   ```

3. **查看帮助**
   ```bash
   python3 batch_workflow_converter.py --help
   ```

## 📁 输出文件说明

### 主要JSON文件 (`*_workflow.json`)
包含完整的工作流配置信息：
- `edges`: 节点之间的连接关系
- `nodes`: 节点的详细信息
- `versions`: 版本信息

### 摘要文件 (`*_summary.json`)
包含文件的基本信息和数据结构说明：
- 文件大小和转换时间
- 数据统计信息
- 使用说明

## 🔧 支持的文件格式

- **ZIP文件**: 包含嵌套MATLAB文件的工作流包
- **MAT文件**: MATLAB格式的工作流文件
- **JSON文件**: 直接的工作流配置文件

## 📊 处理流程

1. **文件检测**: 自动识别支持的文件类型
2. **解压缩**: 如果是ZIP文件，自动解压并查找MATLAB文件
3. **JSON提取**: 从MATLAB文件中提取JSON数据
4. **数据修复**: 自动修复可能的JSON格式问题
5. **文件生成**: 创建格式化的JSON文件和摘要文件

## 🛠️ 故障排除

### 常见问题

1. **"未找到任何ZIP文件"**
   - 确保ZIP文件在脚本同一目录下
   - 检查文件扩展名是否为 `.zip`

2. **"无法提取JSON数据"**
   - 文件可能不是标准的工作流格式
   - 尝试手动检查文件内容

3. **"处理文件失败"**
   - 检查文件是否损坏
   - 确保有足够的磁盘空间

### 日志文件

完整版脚本会生成 `conversion_log.txt` 日志文件，记录详细的处理过程。

## 📝 示例输出

```
🚀 工作流批量转换器
==================================================
输入目录: /Users/<USER>/Downloads
输出目录: /Users/<USER>/Downloads/converted_workflows

找到 3 个ZIP文件:
  - Workflow-DZ052_huochairenxinli2_1-draft-7052.zip
  - Workflow-ABC123_test-001.zip
  - Workflow-XYZ789_production-002.zip

正在处理: Workflow-DZ052_huochairenxinli2_1-draft-7052.zip
✅ 成功转换: Workflow-DZ052_huochairenxinli2_1-draft-7052_workflow.json

正在处理: Workflow-ABC123_test-001.zip
✅ 成功转换: Workflow-ABC123_test-001_workflow.json

正在处理: Workflow-XYZ789_production-002.zip
✅ 成功转换: Workflow-XYZ789_production-002_workflow.json

==================================================
🎉 转换完成！
✅ 成功: 3
❌ 失败: 0
📁 输出目录: /Users/<USER>/Downloads/converted_workflows

📄 生成的JSON文件:
  - Workflow-DZ052_huochairenxinli2_1-draft-7052_workflow.json
  - Workflow-ABC123_test-001_workflow.json
  - Workflow-XYZ789_production-002_workflow.json

📋 生成的摘要文件:
  - Workflow-DZ052_huochairenxinli2_1-draft-7052_summary.json
  - Workflow-ABC123_test-001_summary.json
  - Workflow-XYZ789_production-002_summary.json
```

## 🔍 查看JSON文件

转换后的JSON文件可以用以下工具打开：

- **文本编辑器**: VS Code, Sublime Text, Notepad++
- **JSON查看器**: 在线JSON格式化工具
- **编程IDE**: PyCharm, IntelliJ IDEA
- **系统工具**: macOS的TextEdit, Windows的记事本

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.6+
2. 文件权限是否正确
3. 磁盘空间是否充足
4. 文件格式是否支持

---

**版本**: 1.0  
**更新时间**: 2024-08-07  
**作者**: AI助手 