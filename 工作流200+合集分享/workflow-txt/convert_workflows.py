#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版工作流批量转换器
使用方法：
1. 将工作流ZIP文件放在同一目录下
2. 运行此脚本
3. 查看生成的JSON文件
"""

import os
import json
import zipfile
import shutil
import re
from pathlib import Path
from datetime import datetime

def extract_json_from_file(file_path):
    """从文件中提取JSON数据"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 尝试找到JSON开始的位置
        content_str = content.decode('utf-8', errors='ignore')
        json_start = content_str.find('{')
        
        if json_start == -1:
            return None
        
        # 提取JSON部分
        json_content = content_str[json_start:]
        
        # 尝试解析JSON
        try:
            data = json.loads(json_content)
            return data
        except json.JSONDecodeError:
            # 尝试修复JSON
            fixed_json = fix_json_content(json_content)
            if fixed_json:
                return json.loads(fixed_json)
            return None
            
    except Exception as e:
        print(f"提取JSON失败: {e}")
        return None

def fix_json_content(json_content):
    """尝试修复JSON内容"""
    try:
        # 移除可能的控制字符
        json_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_content)
        
        # 尝试找到完整的JSON对象
        brace_count = 0
        end_pos = 0
        
        for i, char in enumerate(json_content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_pos = i + 1
                    break
        
        if end_pos > 0:
            return json_content[:end_pos]
        
        return None
        
    except Exception as e:
        print(f"修复JSON失败: {e}")
        return None

def process_workflow_file(file_path, output_dir):
    """处理单个工作流文件"""
    file_name = file_path.name
    print(f"正在处理: {file_name}")
    
    try:
        # 检查文件类型
        if file_path.suffix.lower() == '.zip':
            return process_zip_workflow(file_path, output_dir)
        else:
            print(f"跳过不支持的文件类型: {file_name}")
            return False
            
    except Exception as e:
        print(f"处理文件失败 {file_name}: {e}")
        return False

def process_zip_workflow(zip_path, output_dir):
    """处理ZIP工作流文件"""
    try:
        # 创建临时目录
        temp_dir = output_dir / f"temp_{zip_path.stem}"
        temp_dir.mkdir(exist_ok=True)
        
        # 解压ZIP文件
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(temp_dir)
        
        # 查找MATLAB文件
        matlab_files = list(temp_dir.glob("*.zip"))  # 通常是嵌套的ZIP
        if not matlab_files:
            matlab_files = list(temp_dir.glob("*.mat"))
        
        if not matlab_files:
            print(f"在ZIP中未找到MATLAB文件: {zip_path.name}")
            shutil.rmtree(temp_dir)
            return False
        
        # 处理找到的文件
        success = False
        for matlab_file in matlab_files:
            if process_matlab_workflow(matlab_file, zip_path.stem, output_dir):
                success = True
                break
        
        # 清理临时目录
        shutil.rmtree(temp_dir)
        return success
        
    except Exception as e:
        print(f"处理ZIP文件失败 {zip_path.name}: {e}")
        return False

def process_matlab_workflow(file_path, base_name, output_dir):
    """处理MATLAB工作流文件"""
    try:
        # 提取JSON数据
        data = extract_json_from_file(file_path)
        if not data:
            print(f"无法提取JSON数据: {file_path.name}")
            return False
        
        # 生成输出文件名
        output_file = output_dir / f"{base_name}_workflow.json"
        summary_file = output_dir / f"{base_name}_summary.json"
        
        # 保存JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 创建摘要文件
        create_summary_file(data, output_file, summary_file)
        
        print(f"✅ 成功转换: {output_file.name}")
        return True
        
    except Exception as e:
        print(f"处理MATLAB文件失败 {file_path.name}: {e}")
        return False

def create_summary_file(data, json_file, summary_file):
    """创建摘要文件"""
    try:
        summary = {
            "文件信息": {
                "原文件名": json_file.name,
                "文件大小": f"{json_file.stat().st_size / 1024:.1f} KB",
                "转换时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            "数据结构": {
                "edges": {
                    "数量": len(data.get('edges', [])),
                    "描述": "工作流节点之间的连接关系"
                },
                "nodes": {
                    "数量": len(data.get('nodes', [])),
                    "描述": "工作流中的节点"
                },
                "versions": {
                    "内容": data.get('versions', {}),
                    "描述": "版本信息"
                }
            },
            "使用说明": {
                "1": "这是一个工作流配置文件",
                "2": "edges 定义了节点之间的连接关系",
                "3": "nodes 包含了每个节点的详细信息",
                "4": "可以直接用任何JSON编辑器打开查看"
            }
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
            
    except Exception as e:
        print(f"创建摘要文件失败: {e}")

def main():
    """主函数"""
    print("🚀 工作流批量转换器")
    print("=" * 50)
    
    # 设置输入和输出目录
    current_dir = Path(".")
    output_dir = Path("converted_workflows")
    output_dir.mkdir(exist_ok=True)
    
    print(f"输入目录: {current_dir.absolute()}")
    print(f"输出目录: {output_dir.absolute()}")
    print()
    
    # 查找所有ZIP文件
    zip_files = list(current_dir.glob("*.zip"))
    
    if not zip_files:
        print("❌ 未找到任何ZIP文件")
        print("请将工作流ZIP文件放在当前目录下")
        return
    
    print(f"找到 {len(zip_files)} 个ZIP文件:")
    for zip_file in zip_files:
        print(f"  - {zip_file.name}")
    print()
    
    # 处理每个文件
    success_count = 0
    failed_count = 0
    
    for zip_file in zip_files:
        if process_workflow_file(zip_file, output_dir):
            success_count += 1
        else:
            failed_count += 1
    
    # 显示结果
    print()
    print("=" * 50)
    print(f"🎉 转换完成！")
    print(f"✅ 成功: {success_count}")
    print(f"❌ 失败: {failed_count}")
    print(f"📁 输出目录: {output_dir.absolute()}")
    
    # 列出生成的文件
    json_files = list(output_dir.glob("*_workflow.json"))
    if json_files:
        print("\n📄 生成的JSON文件:")
        for json_file in json_files:
            print(f"  - {json_file.name}")
    
    summary_files = list(output_dir.glob("*_summary.json"))
    if summary_files:
        print("\n📋 生成的摘要文件:")
        for summary_file in summary_files:
            print(f"  - {summary_file.name}")

if __name__ == "__main__":
    main() 