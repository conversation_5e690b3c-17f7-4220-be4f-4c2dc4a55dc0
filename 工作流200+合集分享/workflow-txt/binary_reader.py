#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二进制读取MATLAB文件结构
"""

import struct
import numpy as np
import json
import os

def read_matlab_v4_header(file_path):
    """读取MATLAB v4格式文件头"""
    try:
        with open(file_path, 'rb') as f:
            # 读取文件头
            header = f.read(20)
            
            # 解析MATLAB v4格式
            # 前4字节是MOPT参数
            mopt = struct.unpack('>I', header[:4])[0]
            print(f"MOPT: {mopt}")
            
            # 接下来是矩阵类型
            mtype = struct.unpack('>I', header[4:8])[0]
            print(f"矩阵类型: {mtype}")
            
            # 行数
            rows = struct.unpack('>I', header[8:12])[0]
            print(f"行数: {rows}")
            
            # 列数
            cols = struct.unpack('>I', header[12:16])[0]
            print(f"列数: {cols}")
            
            # 是否为复数
            imagf = struct.unpack('>I', header[16:20])[0]
            print(f"是否为复数: {imagf}")
            
            # 计算数据大小
            data_size = rows * cols
            if imagf:
                data_size *= 2  # 复数需要两倍空间
            
            print(f"数据元素数量: {data_size}")
            
            # 尝试读取一些数据样本
            if data_size > 0:
                # 读取前几个元素作为样本
                sample_size = min(100, data_size)
                f.seek(20)  # 跳过头部
                
                try:
                    # 尝试读取数值数据
                    if mtype == 0:  # 数值矩阵
                        if imagf:
                            # 复数矩阵
                            real_data = np.frombuffer(f.read(sample_size * 8), dtype=np.float64)
                            imag_data = np.frombuffer(f.read(sample_size * 8), dtype=np.float64)
                            sample_data = real_data + 1j * imag_data
                        else:
                            # 实数矩阵
                            sample_data = np.frombuffer(f.read(sample_size * 8), dtype=np.float64)
                        
                        print(f"样本数据 (前{sample_size}个):")
                        print(f"  前10个值: {sample_data[:10]}")
                        print(f"  数据类型: {sample_data.dtype}")
                        print(f"  最小值: {np.min(sample_data)}")
                        print(f"  最大值: {np.max(sample_data)}")
                        print(f"  平均值: {np.mean(sample_data):.4f}")
                        
                        # 创建JSON输出
                        result = {
                            "file_info": {
                                "format": "MATLAB v4",
                                "matrix_type": mtype,
                                "rows": int(rows),
                                "columns": int(cols),
                                "is_complex": bool(imagf),
                                "total_elements": int(data_size)
                            },
                            "sample_data": {
                                "first_10_values": sample_data[:10].tolist(),
                                "statistics": {
                                    "min": float(np.min(sample_data)),
                                    "max": float(np.max(sample_data)),
                                    "mean": float(np.mean(sample_data)),
                                    "std": float(np.std(sample_data))
                                }
                            }
                        }
                        
                        # 保存为JSON
                        output_file = "matlab_data_analysis.json"
                        with open(output_file, 'w', encoding='utf-8') as json_file:
                            json.dump(result, json_file, ensure_ascii=False, indent=2)
                        
                        print(f"\n✅ 分析完成，结果保存到: {output_file}")
                        return result
                        
                    else:
                        print(f"不支持的数据类型: {mtype}")
                        
                except Exception as e:
                    print(f"读取数据时出错: {e}")
                    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def analyze_file_structure(file_path):
    """分析文件结构"""
    print(f"分析文件: {file_path}")
    print(f"文件大小: {os.path.getsize(file_path)} 字节")
    
    with open(file_path, 'rb') as f:
        # 读取前100字节
        header = f.read(100)
        print(f"文件头 (hex): {header.hex()}")
        print(f"文件头 (ascii): {repr(header)}")
        
        # 检查文件模式
        f.seek(0)
        mode = struct.unpack('>I', f.read(4))[0]
        print(f"文件模式: {mode}")
        
        # 尝试不同的解析方式
        if mode == 0:
            print("检测到数值矩阵模式")
            return read_matlab_v4_header(file_path)
        else:
            print(f"未知模式: {mode}")
            return None

if __name__ == "__main__":
    matlab_file = "Workflow-DZ052_huochairenxinli2_1-draft-7052.zip"
    
    if os.path.exists(matlab_file):
        result = analyze_file_structure(matlab_file)
        if result:
            print("\n分析结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
    else:
        print(f"文件不存在: {matlab_file}") 