# 优化版工作流批量转换器使用说明

## 🎯 功能概述

这个优化版工作流转换器提供了以下增强功能：

### ✨ 主要改进
- **多格式输出**: 支持JSON、TXT和同时输出两种格式
- **进度显示**: 实时显示转换进度和处理状态
- **增强错误处理**: 更好的错误恢复和详细错误信息
- **多编码支持**: 自动检测和处理不同编码格式的文件
- **用户交互**: 支持用户确认和中断操作
- **详细报告**: 生成完整的转换报告和统计信息

## 🚀 快速开始

### 方法一：使用快速TXT转换器（推荐新手）

```bash
# 1. 将ZIP文件放在脚本同一目录
# 2. 运行快速转换器
python3 quick_txt_converter.py
```

**特点**：
- 操作简单，一键转换
- 专门生成可读的TXT格式
- 自动创建 `txt_workflows` 输出目录
- 适合快速查看工作流内容

### 方法二：使用完整版转换器（推荐高级用户）

```bash
# 基本用法 - 同时生成JSON和TXT
python3 batch_workflow_converter.py

# 只生成TXT格式
python3 batch_workflow_converter.py --format txt

# 只生成JSON格式  
python3 batch_workflow_converter.py --format json

# 指定输入输出目录
python3 batch_workflow_converter.py --input /path/to/input --output /path/to/output

# 显示详细信息
python3 batch_workflow_converter.py --verbose
```

## 📁 输出文件结构

### 完整版转换器输出结构
```
converted_workflows/
├── json/                          # JSON格式文件
│   ├── workflow1_workflow.json    # 主要数据文件
│   ├── workflow1_summary.json     # 摘要信息
│   └── ...
├── txt/                           # TXT格式文件
│   ├── workflow1_workflow.txt     # 可读文本文件
│   └── ...
├── conversion_log.txt             # 详细日志
└── conversion_report.json         # 转换报告
```

### 快速转换器输出结构
```
txt_workflows/
├── workflow1_workflow.txt
├── workflow2_workflow.txt
└── ...
```

## 📄 文件格式说明

### TXT文件内容结构
```
================================================================================
工作流配置文件
================================================================================
原始文件: Workflow-Example.zip
转换时间: 2024-08-08 15:30:45

📋 版本信息:
----------------------------------------
  api: 1.0.0
  app: 2.1.0

🔗 节点信息 (共 5 个节点):
----------------------------------------
节点 1:
  ID: node_1
  类型: input
  位置: (100, 200)
  配置:
    label: 输入节点
    value: 默认值

🔀 连接信息 (共 4 个连接):
----------------------------------------
连接 1:
  ID: edge_1
  源节点: node_1
  目标节点: node_2
  连接端口: output → input

📊 统计信息:
----------------------------------------
  节点总数: 5
  连接总数: 4
  节点类型分布:
    input: 1
    process: 3
    output: 1
```

### JSON文件内容
- **主要JSON文件**: 包含完整的工作流配置数据
- **摘要JSON文件**: 包含文件信息、数据统计和使用说明

## 🔧 支持的文件格式

| 格式 | 描述 | 处理方式 |
|------|------|----------|
| `.zip` | 工作流压缩包 | 自动解压并查找内部文件 |
| `.mat` | MATLAB格式文件 | 直接提取JSON数据 |
| `.json` | JSON配置文件 | 直接处理或格式转换 |

## ⚙️ 高级功能

### 命令行参数详解

```bash
python3 batch_workflow_converter.py [选项]

选项:
  -h, --help            显示帮助信息
  -i, --input DIR       输入目录 (默认: 当前目录)
  -o, --output DIR      输出目录 (默认: converted_workflows)
  -f, --format FORMAT   输出格式: json|txt|both (默认: both)
  -v, --verbose         显示详细日志信息
```

### 批处理示例

```bash
# 处理特定目录的所有工作流
python3 batch_workflow_converter.py -i "/path/to/workflows" -o "/path/to/output"

# 只生成TXT格式，便于阅读
python3 batch_workflow_converter.py -f txt -o "readable_workflows"

# 处理大量文件时显示详细信息
python3 batch_workflow_converter.py -v -f both
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. "未找到任何可处理的文件"
**原因**: 目录中没有支持的文件格式
**解决**: 
- 确保文件扩展名为 `.zip`、`.mat` 或 `.json`
- 检查文件是否在正确的目录中

#### 2. "无法提取JSON数据"
**原因**: 文件格式不标准或损坏
**解决**:
- 检查文件是否完整下载
- 尝试用其他工具打开文件验证
- 查看详细日志了解具体错误

#### 3. "权限不足，无法访问文件"
**原因**: 文件或目录权限问题
**解决**:
```bash
# 修改文件权限
chmod 644 *.zip

# 修改目录权限
chmod 755 input_directory
```

#### 4. "文件过大"警告
**原因**: 文件超过100MB
**解决**: 程序会询问是否继续，根据需要选择

### 错误日志分析

查看 `conversion_log.txt` 文件了解详细错误信息：

```
[2024-08-08 15:30:45] [ERROR] 提取JSON失败: 'utf-8' codec can't decode byte
[2024-08-08 15:30:45] [WARNING] 使用 gbk 编码提取JSON失败: Invalid JSON format
[2024-08-08 15:30:45] [SUCCESS] 成功使用 latin-1 编码提取JSON数据
```

## 📊 性能优化建议

### 处理大量文件时
1. **分批处理**: 将大量文件分成小批次处理
2. **磁盘空间**: 确保有足够的磁盘空间（建议至少是输入文件总大小的3倍）
3. **内存使用**: 大文件可能占用较多内存，建议关闭其他程序

### 提高转换速度
```bash
# 只生成需要的格式
python3 batch_workflow_converter.py -f txt  # 只要TXT格式

# 跳过详细验证（快速模式）
python3 quick_txt_converter.py  # 使用快速转换器
```

## 🔍 输出文件使用建议

### TXT文件
- **查看工具**: 任何文本编辑器（VS Code、Notepad++、TextEdit等）
- **用途**: 快速了解工作流结构和配置
- **优点**: 人类可读，便于分享和讨论

### JSON文件
- **查看工具**: JSON编辑器、IDE、在线JSON格式化工具
- **用途**: 程序处理、详细分析、数据提取
- **优点**: 结构化数据，便于程序处理

## 📞 技术支持

### 系统要求
- **Python版本**: 3.6 或更高
- **操作系统**: Windows、macOS、Linux
- **依赖库**: 仅使用Python标准库，无需额外安装

### 获取帮助
1. **查看帮助**: `python3 batch_workflow_converter.py --help`
2. **检查日志**: 查看 `conversion_log.txt` 文件
3. **验证环境**: 确保Python版本和文件权限正确

---

**版本**: 2.0 优化版  
**更新时间**: 2024-08-08  
**主要改进**: 增加TXT格式支持、进度显示、错误处理优化
