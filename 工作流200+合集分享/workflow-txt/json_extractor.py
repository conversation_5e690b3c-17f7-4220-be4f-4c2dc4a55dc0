#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从文件中提取JSON数据
"""

import json
import re
import os

def extract_json_from_file(file_path):
    """从文件中提取JSON数据"""
    try:
        print(f"正在从文件中提取JSON数据: {file_path}")
        
        with open(file_path, 'rb') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字节")
        
        # 尝试找到JSON开始的位置
        # 从文件头信息看，JSON数据在"workflow"之后
        content_str = content.decode('utf-8', errors='ignore')
        
        # 查找JSON开始的位置
        json_start = content_str.find('{')
        if json_start == -1:
            print("未找到JSON开始标记")
            return None
        
        print(f"找到JSON开始位置: {json_start}")
        
        # 提取JSON部分
        json_content = content_str[json_start:]
        
        # 尝试解析JSON
        try:
            data = json.loads(json_content)
            print("✅ JSON解析成功！")
            
            # 保存完整的JSON文件
            output_file = "extracted_workflow.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ JSON数据已保存到: {output_file}")
            
            # 分析JSON结构
            print("\nJSON数据结构分析:")
            analyze_json_structure(data)
            
            return data
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            
            # 尝试修复JSON
            print("尝试修复JSON...")
            fixed_json = fix_json_content(json_content)
            if fixed_json:
                try:
                    data = json.loads(fixed_json)
                    print("✅ 修复后的JSON解析成功！")
                    
                    output_file = "extracted_workflow_fixed.json"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    print(f"✅ 修复后的JSON数据已保存到: {output_file}")
                    analyze_json_structure(data)
                    return data
                    
                except json.JSONDecodeError as e2:
                    print(f"修复后的JSON仍然无法解析: {e2}")
            
            return None
            
    except Exception as e:
        print(f"处理文件时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_json_content(json_content):
    """尝试修复JSON内容"""
    try:
        # 移除可能的控制字符
        json_content = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_content)
        
        # 尝试找到完整的JSON对象
        brace_count = 0
        end_pos = 0
        
        for i, char in enumerate(json_content):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_pos = i + 1
                    break
        
        if end_pos > 0:
            return json_content[:end_pos]
        
        return None
        
    except Exception as e:
        print(f"修复JSON时出错: {e}")
        return None

def analyze_json_structure(data, max_depth=3, current_depth=0):
    """分析JSON结构"""
    if current_depth >= max_depth:
        print("  " * current_depth + "...")
        return
    
    if isinstance(data, dict):
        print("  " * current_depth + "对象:")
        for key, value in data.items():
            print("  " * (current_depth + 1) + f"{key}: {type(value).__name__}")
            if isinstance(value, (dict, list)) and current_depth < max_depth - 1:
                analyze_json_structure(value, max_depth, current_depth + 1)
    elif isinstance(data, list):
        print("  " * current_depth + f"数组 (长度: {len(data)})")
        if len(data) > 0 and current_depth < max_depth - 1:
            analyze_json_structure(data[0], max_depth, current_depth + 1)
            if len(data) > 1:
                print("  " * (current_depth + 1) + "...")

if __name__ == "__main__":
    matlab_file = "Workflow-DZ052_huochairenxinli2_1-draft-7052.zip"
    
    if os.path.exists(matlab_file):
        result = extract_json_from_file(matlab_file)
        if result:
            print("\n✅ 成功提取JSON数据！")
        else:
            print("❌ 提取失败")
    else:
        print(f"文件不存在: {matlab_file}") 