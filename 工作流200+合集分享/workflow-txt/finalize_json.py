#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成最终的可读JSON文件
"""

import json
import os

def create_readable_json(input_file, output_file):
    """创建可读的JSON文件"""
    try:
        print(f"正在读取: {input_file}")
        
        # 读取JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("✅ JSON数据读取成功")
        
        # 验证JSON结构
        print(f"数据结构:")
        print(f"  - edges: {len(data.get('edges', []))} 个连接")
        print(f"  - nodes: {len(data.get('nodes', []))} 个节点")
        print(f"  - versions: {data.get('versions', {})}")
        
        # 保存为格式化的JSON文件
        print(f"正在保存到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("✅ 可读JSON文件创建成功！")
        
        # 验证文件可以正常打开
        print("验证文件完整性...")
        with open(output_file, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        print("✅ 文件验证通过，可以正常打开和读取")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_summary_file(json_file, summary_file):
    """创建JSON文件摘要"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        summary = {
            "文件信息": {
                "文件名": os.path.basename(json_file),
                "文件大小": f"{os.path.getsize(json_file) / 1024:.1f} KB",
                "创建时间": "2024-08-07"
            },
            "数据结构": {
                "edges": {
                    "数量": len(data.get('edges', [])),
                    "描述": "工作流节点之间的连接关系"
                },
                "nodes": {
                    "数量": len(data.get('nodes', [])),
                    "描述": "工作流中的节点"
                },
                "versions": {
                    "内容": data.get('versions', {}),
                    "描述": "版本信息"
                }
            },
            "使用说明": {
                "1": "这是一个工作流配置文件",
                "2": "edges 定义了节点之间的连接关系",
                "3": "nodes 包含了每个节点的详细信息",
                "4": "可以直接用任何JSON编辑器打开查看"
            }
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 摘要文件已创建: {summary_file}")
        
    except Exception as e:
        print(f"创建摘要文件失败: {e}")

if __name__ == "__main__":
    input_file = "extracted_workflow_fixed.json"
    output_file = "workflow_readable.json"
    summary_file = "workflow_summary.json"
    
    if os.path.exists(input_file):
        if create_readable_json(input_file, output_file):
            create_summary_file(output_file, summary_file)
            print(f"\n🎉 完成！您现在可以打开以下文件:")
            print(f"  📄 主要数据: {output_file}")
            print(f"  📋 文件摘要: {summary_file}")
        else:
            print("❌ 处理失败")
    else:
        print(f"❌ 输入文件不存在: {input_file}") 