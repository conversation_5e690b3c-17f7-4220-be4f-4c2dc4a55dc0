#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查MATLAB文件的基本信息
"""

import scipy.io
import numpy as np
import os
import struct

def inspect_matlab_file(file_path):
    """检查MATLAB文件的基本信息"""
    try:
        print(f"正在检查MATLAB文件: {file_path}")
        print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")
        
        # 尝试读取文件头信息
        with open(file_path, 'rb') as f:
            # 读取MATLAB文件头
            header = f.read(128)
            print(f"文件头: {header[:20]}...")
            
            # 检查是否是MATLAB v4格式
            if header.startswith(b'\x00\x00\x00\x00'):
                print("检测到MATLAB v4格式")
            elif header.startswith(b'MATLAB'):
                print("检测到MATLAB v5/v7格式")
            else:
                print("未知格式")
        
        # 尝试使用scipy读取基本信息
        print("\n尝试读取基本信息...")
        try:
            # 只读取变量名列表，不加载数据
            mat_data = scipy.io.loadmat(file_path, squeeze_me=True, struct_as_record=False)
            print("✅ 成功读取文件")
            print(f"包含的变量: {list(mat_data.keys())}")
            
            # 显示每个变量的基本信息
            for key, value in mat_data.items():
                if not key.startswith('__'):
                    print(f"\n变量: {key}")
                    print(f"  类型: {type(value).__name__}")
                    if hasattr(value, 'shape'):
                        print(f"  形状: {value.shape}")
                        print(f"  大小: {value.size} 元素")
                        print(f"  数据类型: {value.dtype}")
                        
                        # 计算内存使用量
                        if hasattr(value, 'nbytes'):
                            print(f"  内存使用: {value.nbytes / (1024*1024):.2f} MB")
                        
                        # 如果是数值数组，显示一些统计信息
                        if value.dtype.kind in ['i', 'u', 'f']:
                            try:
                                print(f"  最小值: {np.min(value)}")
                                print(f"  最大值: {np.max(value)}")
                                print(f"  平均值: {np.mean(value):.4f}")
                            except:
                                print("  无法计算统计信息")
                    
        except Exception as e:
            print(f"❌ 读取失败: {e}")
            
            # 尝试使用h5py（如果是v7.3格式）
            try:
                import h5py
                print("\n尝试使用h5py读取...")
                with h5py.File(file_path, 'r') as f:
                    print(f"HDF5文件结构: {list(f.keys())}")
                    for key in f.keys():
                        print(f"  {key}: {f[key].shape} {f[key].dtype}")
            except ImportError:
                print("h5py未安装，无法尝试HDF5格式")
            except Exception as e2:
                print(f"h5py读取也失败: {e2}")
        
    except Exception as e:
        print(f"检查文件时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    matlab_file = "Workflow-DZ052_huochairenxinli2_1-draft-7052.zip"
    
    if os.path.exists(matlab_file):
        inspect_matlab_file(matlab_file)
    else:
        print(f"文件不存在: {matlab_file}") 