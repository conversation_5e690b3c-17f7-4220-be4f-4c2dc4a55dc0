#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理MATLAB .mat文件并转换为JSON格式
"""

import scipy.io
import json
import numpy as np
import os
from datetime import datetime

def convert_numpy_to_json(obj):
    """将numpy数组转换为JSON可序列化的格式"""
    if isinstance(obj, np.ndarray):
        if obj.dtype.kind in ['U', 'S']:  # 字符串数组
            return obj.tolist()
        elif obj.dtype.kind in ['i', 'u', 'f']:  # 数值数组
            return obj.tolist()
        elif obj.dtype.kind == 'O':  # 对象数组
            return [convert_numpy_to_json(item) for item in obj]
        else:
            return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_to_json(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: convert_numpy_to_json(value) for key, value in obj.items()}
    else:
        return obj

def process_matlab_file(file_path):
    """处理MATLAB文件并转换为JSON"""
    try:
        print(f"正在读取MATLAB文件: {file_path}")
        
        # 读取MATLAB文件
        mat_data = scipy.io.loadmat(file_path)
        
        print("文件读取成功！")
        print(f"包含的键: {list(mat_data.keys())}")
        
        # 过滤掉系统变量（以__开头的键）
        filtered_data = {}
        for key, value in mat_data.items():
            if not key.startswith('__'):
                print(f"处理键: {key}, 类型: {type(value).__name__}, 形状: {getattr(value, 'shape', 'N/A')}")
                filtered_data[key] = convert_numpy_to_json(value)
        
        # 生成输出文件名
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_file = f"{base_name}_converted.json"
        
        # 保存为JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成！输出文件: {output_file}")
        
        # 显示数据摘要
        print("\n数据摘要:")
        for key, value in filtered_data.items():
            if isinstance(value, list):
                print(f"  {key}: 列表，长度 {len(value)}")
                if len(value) > 0:
                    print(f"    第一个元素类型: {type(value[0]).__name__}")
            else:
                print(f"  {key}: {type(value).__name__}")
        
        return output_file
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

if __name__ == "__main__":
    # 处理当前目录下的MATLAB文件
    matlab_file = "Workflow-DZ052_huochairenxinli2_1-draft-7052.zip"
    
    if os.path.exists(matlab_file):
        output_file = process_matlab_file(matlab_file)
        if output_file:
            print(f"\n✅ 成功将MATLAB文件转换为JSON: {output_file}")
        else:
            print("❌ 转换失败")
    else:
        print(f"文件不存在: {matlab_file}") 